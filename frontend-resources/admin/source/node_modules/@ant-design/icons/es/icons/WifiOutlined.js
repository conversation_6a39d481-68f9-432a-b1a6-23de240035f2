import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import WifiOutlinedSvg from "@ant-design/icons-svg/es/asn/WifiOutlined";
import AntdIcon from "../components/AntdIcon";
var WifiOutlined = function WifiOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: WifiOutlinedSvg
  }));
};

/**![wifi](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTcyMyA2MjAuNUM2NjYuOCA1NzEuNiA1OTMuNCA1NDIgNTEzIDU0MnMtMTUzLjggMjkuNi0yMTAuMSA3OC42YTguMSA4LjEgMCAwMC0uOCAxMS4ybDM2IDQyLjljMi45IDMuNCA4IDMuOCAxMS40LjlDMzkzLjEgNjM3LjIgNDUwLjMgNjE0IDUxMyA2MTRzMTE5LjkgMjMuMiAxNjMuNSA2MS41YzMuNCAyLjkgOC41IDIuNSAxMS40LS45bDM2LTQyLjljMi44LTMuMyAyLjQtOC4zLS45LTExLjJ6bTExNy40LTE0MC4xQzc1MS43IDQwNi41IDYzNy42IDM2MiA1MTMgMzYycy0yMzguNyA0NC41LTMyNy41IDExOC40YTguMDUgOC4wNSAwIDAwLTEgMTEuM2wzNiA0Mi45YzIuOCAzLjQgNy45IDMuOCAxMS4yIDFDMzA4IDQ3Mi4yIDQwNi4xIDQzNCA1MTMgNDM0czIwNSAzOC4yIDI4MS4yIDEwMS42YzMuNCAyLjggOC40IDIuNCAxMS4yLTFsMzYtNDIuOWMyLjgtMy40IDIuNC04LjUtMS0xMS4zem0xMTYuNy0xMzlDODM1LjcgMjQxLjggNjgwLjMgMTgyIDUxMSAxODJjLTE2OC4yIDAtMzIyLjYgNTktNDQzLjcgMTU3LjRhOCA4IDAgMDAtMS4xIDExLjRsMzYgNDIuOWMyLjggMy4zIDcuOCAzLjggMTEuMSAxLjFDMjIyIDMwNi43IDM2MC4zIDI1NCA1MTEgMjU0YzE1MS44IDAgMjkxIDUzLjUgNDAwIDE0Mi43IDMuNCAyLjggOC40IDIuMyAxMS4yLTEuMWwzNi00Mi45YzIuOS0zLjQgMi40LTguNS0xLjEtMTEuM3pNNDQ4IDc3OGE2NCA2NCAwIDEwMTI4IDAgNjQgNjQgMCAxMC0xMjggMHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(WifiOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'WifiOutlined';
}
export default RefIcon;