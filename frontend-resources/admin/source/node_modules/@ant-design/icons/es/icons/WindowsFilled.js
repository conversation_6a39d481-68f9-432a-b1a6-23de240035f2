import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import WindowsFilledSvg from "@ant-design/icons-svg/es/asn/WindowsFilled";
import AntdIcon from "../components/AntdIcon";
var WindowsFilled = function WindowsFilled(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: WindowsFilledSvg
  }));
};

/**![windows](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUyMy44IDE5MS40djI4OC45aDM4MlYxMjguMXptMCA2NDIuMmwzODIgNjIuMnYtMzUyaC0zODJ6TTEyMC4xIDQ4MC4ySDQ0M1YyMDEuOWwtMzIyLjkgNTMuNXptMCAyOTAuNEw0NDMgODIzLjJWNTQzLjhIMTIwLjF6IiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(WindowsFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'WindowsFilled';
}
export default RefIcon;