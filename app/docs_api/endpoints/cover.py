# 本文件用于存放以下接口：
# - 生成项目名称 /project_name
# - 生成项目编制、校核、审核人员 /project_staff
import json
import os
import re
import shutil
import pandas as pd
import pathlib
from app.core.config import settings
from app.core.logging_config import get_logger
from app.utils.database import DatabaseManager
from app.utils.response import APIResponse, api_response
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional
from app.docs_api.schemas import ProjectInfoRequest

logger = get_logger(__name__)

router = APIRouter()


class ProjectNameRequest(BaseModel):
    """项目名称请求模型"""
    username: str
    project_name: str = Field(
        ...,
        description="项目名称，格式为：项目类（具体项目名称）",
        example="数字化转型（智能电网管理系统）"
    )


class ProjectStaffRequest(BaseModel):
    """项目人员请求模型"""
    username: str = Field(
        ...,
        description="编写人员用户名",
        example="zhangsan"
    )


class APIResponseModel(BaseModel):
    """标准API响应模型"""
    status_code: int = Field(..., description="状态码")
    message: str = Field(..., description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


class ProjectNameResponse(BaseModel):
    """项目名称响应数据模型"""
    project_name: str = Field(..., description="完整的项目名称")
    category: Optional[Dict[str, str]] = Field(None, description="项目分类信息")


class ProjectFullNameResponse(BaseModel):
    """项目全称响应数据模型"""
    construction_unit: str = Field(..., description="建设单位完整名称")


class ProjectStaffResponse(BaseModel):
    """项目人员响应数据模型"""
    staffs: Dict[str, str] = Field(..., description="各角色人员信息")


def read_project_types():
    # 读取项目类
    file_path = os.path.join(settings.PROJECT_ROOT, 'documents', '项目类.txt')
    with open(file_path, 'r', encoding='utf-8') as file:
        return [line.strip() for line in file if line.strip()]


def parse_project_name(project_name):
    # 解析用户输入的项目名称，获取项目类和具体的项目名称
    pattern = r'^(.*)\s*[（(]([^()（）]*)[\)）]$'
    match = re.match(pattern, project_name)

    if match:
        project_type = match.group(1).strip()
        project_name = match.group(2).strip()
        return project_type, project_name
    else:
        # 如果无法解析出项目类和项目名称，则默认项目类为None，项目名称为用户输入的项目名称
        return None, project_name


def get_category_info(project_name: str):
    # 根据项目名称返回对应分类信息
    try:
        category_dict = {}
        project_type, project_name = parse_project_name(project_name)
        project_directory = os.path.join(
            settings.PROJECT_ROOT, 'documents', 'tugraph', 'entity', '初始表-规划项目表.csv')
        project_df = pd.read_csv(project_directory, encoding='utf-8')
        project_df = project_df[project_df['项目类'] == project_type]
        # 判断是否找到对应项目类
        if project_df.empty:
            category_dict['一级分类'] = ''
            category_dict['二级分类'] = ''
            category_dict['三级分类'] = ''
        else:
            category_dict['一级分类'] = project_df['一级分类'].values[0]
            category_dict['二级分类'] = project_df['二级分类'].values[0]
            category_dict['三级分类'] = project_df['三级分类'].values[0]
        category_dict['项目类'] = project_type
        category_dict['项目名称'] = project_name

        return category_dict

    except Exception as e:
        logger.info(f"Error: {e}")
        return {}


@router.post(
    "/project_name",
    summary="校验并补全项目名称",
    description="将用户输入的项目名称与项目库进行校验，并按照标准格式补全完整名称",
    response_model=APIResponseModel,
    responses={
        200: {
            "description": "校验成功，返回完整项目名称和分类信息",
            "content": {
                "application/json": {
                    "example": {
                        "status_code": 200,
                        "message": "项目名称校对并补全成功",
                        "data": {
                            "project_name": "数字化转型（智能电网管理系统）建设项目可行性研究报告",
                            "category": {
                                "一级分类": "信息化",
                                "二级分类": "数字化转型",
                                "三级分类": "智能电网",
                                "项目类": "数字化转型",
                                "项目名称": "智能电网管理系统"
                            }
                        }
                    }
                }
            }
        },
        201: {
            "description": "项目类不在项目库中，只返回项目名称",
            "content": {
                "application/json": {
                    "example": {
                        "status_code": 201,
                        "message": "项目类不在项目库中，只返回项目名称",
                        "data": {
                            "project_name": "智能电网管理系统"
                        }
                    }
                }
            }
        },
        422: {
            "description": "项目名称格式错误",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "项目名称不符合要求，请确保格式为：项目类（项目名称）"
                    }
                }
            }
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "服务器内部错误"
                    }
                }
            }
        }
    }
)
@api_response
async def get_project_name(input: ProjectNameRequest):
    """
    ## 功能说明
    将用户输入的项目名称与项目库进行校验，并按照标准格式补全完整名称。
    项目名称必须符合"项目类（具体项目名称）"的格式，其中项目类必须存在于项目库中。

    ## 请求参数
    - **project_name**: 项目名称，格式为"项目类（具体项目名称）"
      - 示例：`"数字化转型（智能电网管理系统）"`
      - 注意：必须包含中文括号，项目类必须在项目库中存在

    ## 响应说明
    ### 成功响应 (200)
    - 项目类在项目库中找到，返回完整项目名称和分类信息
    - 项目名称会自动添加"建设项目可行性研究报告"后缀

    ### 部分成功响应 (201)
    - 项目类不在项目库中，只返回具体项目名称部分
    - 不会添加后缀和分类信息

    ## 错误处理
    - **422**: 项目名称格式不正确，缺少括号或格式不符合要求
    - **500**: 服务器内部处理错误，如文件读取失败等
    """
    # 首先对project_name进行解析，获取项目类和项目名称
    project_type, project_name = parse_project_name(input.project_name)

    # 如果项目类为空，则说明项目名称不符合要求
    if not project_type:
        raise HTTPException(
            status_code=422, detail="项目名称不符合要求，请确保格式为：项目类（项目名称）")

    # 读取项目类
    project_types = read_project_types()

    # 检查项目类是否在项目类列表中,如果不在，只返回project_name
    if project_type not in project_types:
        return APIResponse(201, "项目类不在项目库中，只返回项目名称", {"project_name": project_name})

    # 如果检查通过，返回补全后的项目名称以及分类信息
    project_name = f"{input.project_name}建设项目可行性研究报告"
    category = get_category_info(input.project_name)
    
    # 如果user_project_directory已存在，说明该用户已有同名项目，需删除user_project_directory文件夹
    user_project_directory = os.path.join(
        settings.UPLOAD_DIRECTORY, input.username, input.project_name)
    if os.path.exists(user_project_directory):
        shutil.rmtree(user_project_directory)

    return APIResponse(200, "项目名称校对并补全成功", {"project_name": project_name, "category": category})


@router.post(
    "/project_full_name",
    summary="生成可研封面专用的建设单位名称",
    description="根据项目配置信息生成完整的建设单位名称，用于可研封面显示",
    response_model=APIResponseModel,
    responses={
        200: {
            "description": "成功获取建设单位名称",
            "content": {
                "application/json": {
                    "examples": {
                        "single_unit": {
                            "summary": "单个建设单位",
                            "value": {
                                "status_code": 200,
                                "message": "success",
                                "data": {
                                    "construction_unit": "广东电网有限责任公司"
                                }
                            }
                        },
                        "multiple_units": {
                            "summary": "多个建设单位（使用默认）",
                            "value": {
                                "status_code": 200,
                                "message": "项目配置文件中存在多个建设单位，使用默认建设单位",
                                "data": {
                                    "construction_unit": "中国南方电网有限责任公司"
                                }
                            }
                        },
                        "no_config": {
                            "summary": "无配置文件（使用默认）",
                            "value": {
                                "status_code": 200,
                                "message": "项目配置文件不存在，使用默认建设单位",
                                "data": {
                                    "construction_unit": "中国南方电网有限责任公司"
                                }
                            }
                        }
                    }
                }
            }
        },
        404: {
            "description": "未找到建设单位简称对应的全称",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "未找到建设单位 粤电网 的全称信息"
                    }
                }
            }
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "服务器内部处理错误"
                    }
                }
            }
        }
    }
)
@api_response
async def get_project_full_name(input: ProjectInfoRequest):
    """
    ## 功能说明
    根据项目配置信息生成完整的建设单位名称，用于可研封面显示。

    ## 处理逻辑
    1. **单个建设单位**: 从简称映射表中查找对应的全称
    2. **多个建设单位**: 使用默认建设单位"中国南方电网有限责任公司"
    3. **无配置文件**: 使用默认建设单位"中国南方电网有限责任公司"

    ## 请求参数
    - **username**: 用户名，用于定位项目配置文件
      - 示例：`"zhangsan"`
    - **project_name**: 项目名称，用于定位项目配置文件
      - 示例：`"智能电网管理系统"`

    ## 配置文件路径
    配置文件位置：`uploads/{username}/{project_name}/config.json`

    ## 响应说明
    ### 成功响应 (200)
    返回建设单位的完整名称，根据不同情况返回不同的message：
    - `"success"`: 成功从单个建设单位简称获取全称
    - `"项目配置文件中存在多个建设单位，使用默认建设单位"`: 多单位情况
    - `"项目配置文件不存在，使用默认建设单位"`: 无配置文件情况

    ## 错误处理
    - **404**: 建设单位简称在映射表中不存在
    - **500**: 配置文件读取错误或其他服务器内部错误
    """
    # Define constant for default unit
    DEFAULT_CONSTRUCTION_UNIT = "中国南方电网有限责任公司"

    try:
        base_project_path = pathlib.Path(
            settings.PROJECT_ROOT) / settings.UPLOAD_DIRECTORY / input.username / input.project_name
        config_path = base_project_path / 'config.json'
        unit_mapping_path = pathlib.Path(
            settings.PROJECT_ROOT) / 'documents' / '公司简称-全称对应表.json'

        construction_unit_full = DEFAULT_CONSTRUCTION_UNIT
        message = "项目配置文件不存在，使用默认建设单位"  # Default message if config doesn't exist

        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as file:
                    config = json.load(file)

                # Use .get() for safer dictionary access
                construction_unit_str = config.get('construction_unit')
                if not construction_unit_str:
                    raise KeyError("配置文件中缺少 'construction_unit' 字段")

                construction_units = construction_unit_str.split('、')

                if len(construction_units) == 1:
                    construction_unit_abbr = construction_units[0]

                    if not unit_mapping_path.exists():
                        # This is a server configuration issue
                        raise FileNotFoundError(
                            f"单位简称映射文件未找到: {unit_mapping_path}")

                    with open(unit_mapping_path, 'r', encoding='utf-8') as f_map:
                        investment_units = json.load(f_map)

                    unit_full = investment_units.get(construction_unit_abbr)
                    if not unit_full:
                        # Abbreviation not found in mapping - potentially bad data in config
                        raise ValueError(
                            f"未找到建设单位 {construction_unit_abbr} 的全称信息")

                    construction_unit_full = unit_full
                    message = "success"  # Update message on successful single unit lookup
                else:
                    message = "项目配置文件中存在多个建设单位，使用默认建设单位"

            except (FileNotFoundError, json.JSONDecodeError, KeyError) as e:
                # Consolidate internal errors related to config/mapping file processing
                logger.error(
                    f"Error processing project config or mapping file: {e}")
                raise HTTPException(status_code=500, detail="服务器内部处理错误")
            except ValueError as e:
                # Catch the specific ValueError for missing unit abbreviation -> Not Found Error
                logger.warning(f"Value error during unit lookup: {e}")
                raise HTTPException(status_code=404, detail=str(e))

        # Return the determined unit and message
        return APIResponse(200, message, {"construction_unit": construction_unit_full})

    except HTTPException as e:
        # Re-raise known HTTP exceptions cleanly
        raise e
    except Exception as e:
        # Catch-all for unexpected errors
        logger.exception(
            f"Unexpected error in get_project_full_name for user {input.username}, project {input.project_name}")  # Log context
        raise HTTPException(status_code=500, detail="服务器内部错误")


@router.post(
    "/project_staff",
    summary="生成批准、审核、校核、编写人员",
    description="根据编写人员用户名生成项目的批准、审核、校核和编写人员信息",
    response_model=APIResponseModel,
    responses={
        200: {
            "description": "成功获取项目人员信息",
            "content": {
                "application/json": {
                    "examples": {
                        "two_char_name": {
                            "summary": "两字姓名（格式化显示）",
                            "value": {
                                "status_code": 200,
                                "message": "获取角色信息成功",
                                "data": {
                                    "staffs": {
                                        "批  准": "陈志坚",
                                        "审  核": "冯国平",
                                        "校  核": "黄立冬",
                                        "编  写": "李  明"
                                    }
                                }
                            }
                        },
                        "three_char_name": {
                            "summary": "三字姓名（正常显示）",
                            "value": {
                                "status_code": 200,
                                "message": "获取角色信息成功",
                                "data": {
                                    "staffs": {
                                        "批  准": "陈志坚",
                                        "审  核": "冯国平",
                                        "校  核": "黄立冬",
                                        "编  写": "王小明"
                                    }
                                }
                            }
                        },
                        "other_length_name": {
                            "summary": "其他长度姓名（分散显示）",
                            "value": {
                                "status_code": 200,
                                "message": "获取角色信息成功",
                                "data": {
                                    "staffs": {
                                        "批  准": "陈 志 坚",
                                        "审  核": "冯 国 平",
                                        "校  核": "黄 立 冬",
                                        "编  写": "欧阳小明"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        404: {
            "description": "未找到该用户信息",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "未找到该用户信息"
                    }
                }
            }
        },
        500: {
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "数据库连接失败"
                    }
                }
            }
        }
    }
)
@api_response
async def get_roles(input: ProjectStaffRequest):
    """
    ## 功能说明
    根据编写人员用户名生成项目的批准、审核、校核和编写人员信息。

    ## 处理逻辑
    1. 根据用户名从数据库查询用户的真实姓名
    2. 根据姓名长度进行格式化处理：
       - **2个字**: 格式化为 "李  明"（中间两个空格）
       - **3个字**: 保持原样 "王小明"
       - **其他长度**: 固定角色使用分散格式 "陈 志 坚"

    ## 请求参数
    - **username**: 编写人员用户名
      - 示例：`"zhangsan"`
      - 说明：用于从数据库查询对应的真实姓名

    ## 固定角色人员
    - **批准**: 陈志坚
    - **审核**: 冯国平
    - **校核**: 黄立冬
    - **编写**: 根据用户名查询的真实姓名

    ## 响应说明
    ### 成功响应 (200)
    返回包含四个角色的人员信息字典，编写人员根据姓名长度进行相应格式化。

    ## 数据库依赖
    需要查询 `users` 表的 `full_name` 字段，确保用户信息存在。

    ## 错误处理
    - **404**: 用户名在数据库中不存在
    - **500**: 数据库连接失败或其他服务器内部错误
    """
    try:
        # 创建数据库连接
        db = DatabaseManager(
            host=settings.DB_HOST,
            port=settings.DB_PORT,
            user=settings.DB_USER,
            password=settings.DB_PASSWORD,
            database=settings.DB_NAME
        )
        db.connect()
        # 根据 username 查找对应的 full_name
        query = "SELECT full_name FROM users WHERE username = %s"
        result = db.execute_query(query, (input.username,))
        db.disconnect()

        if not result:
            raise HTTPException(status_code=404, detail="未找到该用户信息")

        full_name = result[0]["full_name"]

        # 根据 full_name 的长度来决定格式
        if len(full_name) == 2:  # 两个汉字，格式化为 "李 明"
            full_name = f"{full_name[0]}  {full_name[1]}"  # 直接将两个字连接，中间有两个空格
            staffs = {
                "批  准": "陈志坚",
                "审  核": "冯国平",
                "校  核": "黄立冬",
                "编  写": full_name  # 使用从数据库查询到的 full_name
            }

        elif len(full_name) == 3:  # 三个汉字，不变
            staffs = {
                "批  准": "陈志坚",
                "审  核": "冯国平",
                "校  核": "黄立冬",
                "编  写": full_name  # 使用从数据库查询到的 full_name
            }

        else:
            staffs = {
                "批  准": "陈 志 坚",
                "审  核": "冯 国 平",
                "校  核": "黄 立 冬",
                "编  写": full_name  # 使用从数据库查询到的 full_name
            }

        return APIResponse(200, "获取角色信息成功", {"staffs": staffs})

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
