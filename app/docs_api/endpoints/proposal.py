# 本文件用于存放以下接口：
# - 生成业务架构-业务能力 /business_architecture/business_capability
# - 生成业务架构-业务流程 /business_architecture/business_process
# - 生成业务架构-业务流程协作情况 /business_architecture/business_process_status
# - 生成应用架构-应用模块 /application_architecture/application_module
# - 生成应用架构-应用功能 /application_architecture/application_functionality
# - 生成应用架构-应用交互 /application_architecture/application_interaction
# - 生成应用架构-业务与应用对应情况 /application_architecture/business_application_relationship
# - 生成数据架构-数据域 /data_architecture/data_domain
# - 生成数据架构-逻辑实体 /data_architecture/logical_entity
# - 生成数据架构-逻辑实体分布 /data_architecture/logical_entity_distribution
# - 生成技术架构-技术分类 /technical_architecture/technical_classification
# - 生成系统部署方式及软硬件资源需求-部署方式 /system_deployment/deployment_method
# - 生成系统部署方式及软硬件资源需求-软硬件资源需求 /system_deployment/hardware_software_requirements
# - 生成安全技术方案 /security_technology_solution
# - 生成项目实施需求-实施策略 /implementation_requirements/implementation_strategy
# - 生成项目实施需求-实施计划 /implementation_requirements/implementation_plan
# - 生成项目实施需求-实施任务分解 /implementation_requirements/implementation_task_decomposition

# 导入相关依赖
import os
import re
import ast
import xlrd
import difflib
import pandas as pd
from app.core.logging_config import get_logger
from openpyxl import load_workbook
from app.core.config import Settings
from app.utils.llm import chat_with_llm
from fastapi import APIRouter, HTTPException
from app.utils.excel_processor import read_app_func, replace_standalone
from app.utils.response import APIResponse, api_response
from app.utils.doc_processor import STATIC_URL, docx_search, read_config
from app.templates.prompt_templates import extract_business_capability_prompt, identify_type_prompt
from app.docs_api.schemas import ProjectInfoRequest, SystemLevelRequest, DeploymentMethodRequest

router = APIRouter()
logger = get_logger(__name__)

# 读取项目根目录
PROJECT_ROOT = Settings().PROJECT_ROOT
UPLOAD_DIRECTORY = Settings().UPLOAD_DIRECTORY
STATIC_URL = Settings().STATIC_URL

def read_document(document_path):
    # 读取文件内容
    file_path = os.path.join(PROJECT_ROOT, 'documents', document_path)
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    return content


# 处理句子内容返回列表格式
def parse_document(content: str):
    result = []
    lines = content.splitlines()

    for line in lines:
        line = line.strip()
        if not line:
            continue  # 跳过空行

        # 处理包含数字前缀的标题
        match = re.match(r"^(\d+(\.\d+)+)(.*)", line)
        if match:
            # 获取数字部分和标题内容部分
            title_number = match.group(1)  # e.g. "3.5", "3.5.1"
            title_content = match.group(3).strip()  # 去掉数字后的标题内容

            # 根据标题的层级设置 style
            if title_number.count('.') == 1:
                result.append({"style": "2", "content": title_content})
            elif title_number.count('.') == 2:
                result.append({"style": "3", "content": title_content})
            elif title_number.count('.') == 3:
                result.append({"style": "4", "content": title_content})
        else:
            # 正文内容，标记为 style 0
            result.append({"style": "0", "content": line})

    return result


# 根据实体名称查询关系表
def find_relation(realtion_df, entity_name):
    """
    在关系数据中查找实体名称，并返回相应的关系列表。

    参数:
        realation_df (pd.DataFrame): 包含head_id, head_name, tail_id, tail_name, name的DataFrame。
        entity_name (str): 需要查询的实体名称。

    返回:
        relation_list (list): 包含查询结果或与查询结果关联的头尾tuple组成的list。
    """
    # 检查item_name是否在head_name或tail_name中
    found_in_head = realtion_df[realtion_df['head_name'] == entity_name]
    found_in_tail = realtion_df[realtion_df['tail_name'] == entity_name]

    relation_list = []
    if not found_in_head.empty:
        for _, row in found_in_head.iterrows():
            relation_list.append((row['head_name'], row['tail_name']))
    if not found_in_tail.empty:
        for _, row in found_in_tail.iterrows():
            relation_list.append((row['head_name'], row['tail_name']))
    return relation_list


# 读取工作表的内容到二维列表中
def read_sheet(sheet):
    """读取工作表的内容到二维列表中"""
    data = []
    if isinstance(sheet, xlrd.sheet.Sheet):
        for row_idx in range(sheet.nrows):
            row_data = [str(sheet.cell(row_idx, col_idx).value).strip() if sheet.cell(
                row_idx, col_idx).value is not None else "" for col_idx in range(sheet.ncols)]
            data.append(row_data)
    else:
        for row in sheet.iter_rows(values_only=True):
            row_data = [str(cell).strip()
                        if cell is not None else "" for cell in row]
            data.append(row_data)
    return data


def sheet_search(username, project_name, sheet_name):
    '''
    根据用户项目及工作簿名获取表格内容

    参数：
        username (str): 用户名称
        project_name (str): 项目名称
        sheet_name (str): 工作簿名称

    返回：
        data (list): 返回表格的二维列表或者None
    '''
    file_directory = os.path.join(UPLOAD_DIRECTORY, username, project_name)
    for file_name in os.listdir(file_directory):
        if file_name.endswith('.xlsx') or file_name.endswith('xls'):
            file_path = os.path.join(file_directory, file_name)
            if file_path.endswith('.xlsx'):
                workbook = load_workbook(file_path)
                if sheet_name in workbook.sheetnames:
                    return read_sheet(workbook[sheet_name])
            else:
                workbook = xlrd.open_workbook(file_path)
                if sheet_name in workbook.sheet_names():
                    return read_sheet(workbook.sheet_by_name(sheet_name))
    return None


def extract_and_insert(content_list, deployment_content):
    # 提取 style 为 -1 或 content 包含 "图+数字" 格式的字典
    extracted = [
        item for item in content_list
        if item["style"] == "-1" or re.search(r"图\d+(\s+\S*)?", item["content"])
    ]

    # 如果 extracted 列表不为空，插入到 deployment_content 的第三和第四字典之间
    if extracted:
        # 保证 list 中的第三个字典和第四个字典存在
        if len(deployment_content) >= 4:
            deployment_content = deployment_content[:3] + \
                extracted + deployment_content[3:]
        else:
            deployment_content.extend(extracted)  # 如果没有第四个字典，就直接追加

    return deployment_content


@router.post("/business_architecture/business_capability", summary="业务架构-业务能力")
@api_response
async def business_capability(request: ProjectInfoRequest):
    """
    生成给定的业务架构-业务能力。
    """
    try:
        business_capability = docx_search(
            request.username, request.project_name, '业务能力', format='list')
        if not business_capability:
            return APIResponse(200, "未识别出业务能力", business_capability)
        # 提取搜索结果中的纯文本
        business_capability_text = [
            content['content'] for content in business_capability if content['style'] == '0']
        business_capability_text = '\n'.join(business_capability_text)

        # 调用LLM模型提取业务能力并处理返回结果
        llm_response = chat_with_llm(
            extract_business_capability_prompt + business_capability_text)
        try:
            # 尝试直接用 ast.literal_eval 解析
            business_capability_list = ast.literal_eval(llm_response)
        except (ValueError, SyntaxError):
            # 如果解析失败，尝试清理和标准化文本
            cleaned_response = (llm_response
                                .replace('。', '.')  # 替换中文句号
                                .replace('，', ',')  # 替换中文逗号
                                .replace('、', ',')  # 替换顿号
                                .replace('：', ':')  # 替换中文冒号
                                .replace('"', '"')   # 替换中文引号
                                .replace('"', '"'))  # 替换中文引号

            try:
                business_capability_list = ast.literal_eval(cleaned_response)
            except (ValueError, SyntaxError):
                # 如果仍然失败，使用简单的分割方法
                business_capability_list = [
                    item.strip() for item in cleaned_response.strip('[]').split(',')
                    if item.strip()
                ]

        # 读取现有的所有业务能力名称
        csv_path = os.path.join(PROJECT_ROOT, 'documents', '业务域-业务能力.csv')
        existing_business_capability_list = list(
            pd.read_csv(csv_path)['业务能力名'])

        # 查找不在existing_business_capability_list的业务能力
        new_business_capability_list = [
            capability for capability in business_capability_list if capability not in existing_business_capability_list]

        # 如果不存在新的业务能力，则直接返回business_capability的内容
        if not new_business_capability_list:
            return APIResponse(200, "生成业务能力成功", business_capability)

        # 否则，在business_capability的第一个元素后面加入批注，提示有新的业务能力
        # business_capability.insert(
        #     1, {'style': '-5', 'content': f'检测到新的业务能力，请予以确认：{"、".join(new_business_capability_list)}'})
        return APIResponse(200, "生成业务能力成功", business_capability)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/business_architecture/business_process_collaboration_status", summary="业务架构-业务流程协作情况")
@api_response
async def business_process_collaboration_status(request: ProjectInfoRequest):
    """
    生成给定的业务架构-业务流程协作情况。
    """
    try:
        return APIResponse(200, "生成业务流程协作情况成功", docx_search(
            request.username, request.project_name, '业务流程协作情况'))

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/data_architecture/data_domain", summary="数据架构-数据域")
@api_response
async def data_domain(request: ProjectInfoRequest):
    """
    生成给定的数据架构-数据域。
    """
    try:
        data_domain = docx_search(
            request.username, request.project_name, '数据域')
        if not data_domain:
            return APIResponse(200, "未识别出word中数据域相关内容", [{"style": "-5", "content": "资料中未识别到数据域相关内容！"}])
        return APIResponse(200, "生成数据域成功", data_domain)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/data_architecture/logical_entity", summary="数据架构-逻辑实体")
@api_response
async def logical_entity(request: ProjectInfoRequest):
    """
    生成给定的数据架构-逻辑实体。
    """
    try:
        logical_entity = docx_search(
            request.username, request.project_name, '逻辑实体')
        if not logical_entity:
            # 尝试读取用户项目目录下的Excel文件中的逻辑实体
            log_list = sheet_search(
                request.username, request.project_name, '数据架构-应用功能与逻辑实体对应关系')
            abs_list = sheet_search(
                request.username, request.project_name, '数据架构-逻辑实体与属性对应关系')
            if not log_list or not abs_list:
                return APIResponse(404, "未识别出逻辑实体", None)
            elif log_list and abs_list:
                logical_entity = {
                    "style": "-3", "content": log_list, "style": "-3", "content": abs_list}
            elif log_list and not abs_list:
                logical_entity = {"style": "-3", "content": log_list}
            elif not log_list and abs_list:
                logical_entity = {"style": "-3", "content": abs_list}

        # 如果逻辑实体内容不为空，则返回
        if logical_entity:
            return APIResponse(200, "生成逻辑实体成功", logical_entity)
        else:
            return APIResponse(200, "未识别出word中逻辑实体相关内容", [{"style": "-5", "content": "资料中未识别到逻辑实体相关内容！"}])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/data_architecture/logical_entity_distribution", summary="数据架构-逻辑实体分布")
@api_response
async def logical_entity_distribution(request: ProjectInfoRequest):
    """
    生成给定的数据架构-逻辑实体分布。
    """
    try:
        logical_entity_distribution = docx_search(
            request.username, request.project_name, '逻辑实体分布')
        if not logical_entity_distribution:
            return APIResponse(200, "未识别出word中逻辑实体分布相关内容", [{"style": "-5", "content": "资料中未识别到逻辑实体分布相关内容！"}])
        return APIResponse(200, "生成逻辑实体分布成功", logical_entity_distribution)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/technical_architecture/technical_classification", summary="技术架构-技术分类")
@api_response
async def technical_classification(request: ProjectInfoRequest):
    """
    生成给定的技术架构-技术分类。
    """
    try:
        tech_arch = docx_search(request.username, request.project_name, '技术分类')
        if not tech_arch:
            return APIResponse(200, "未识别出word中技术分类相关内容", [{"style": "-5", "content": "资料中未识别到技术分类相关内容！"}])
        return APIResponse(200, "生成技术分类成功", tech_arch)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/system_deployment/deployment_method", summary="生成系统部署方式及软硬件资源需求-部署方式")
async def generate_deployment_method(deployment_request: DeploymentMethodRequest):
    """
    生成系统部署方式及软硬件资源需求-部署方式。

    参数:
    - **deployment_request**: DeploymentMethodRequest 对象，包含部署模式、用户名和项目名称

    返回:
    - **deployment_method**: 生成的系统部署方式内容

    异常:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        # 初始化
        mark_content = ""
        deployment_method = []
        username = deployment_request.username
        project_name = deployment_request.project_name
        deploy_mode = deployment_request.deployment_mode

        # 确认部署模式和项目类型
        valid_deployment_modes = [
            "网一级部署模式", "网省两级部署模式", "省一级部署模式",
            "省地两级部署模式", "省地县三级部署模式", "网一级管理节点和省一级分节点部署模式"
        ]
        if deploy_mode not in valid_deployment_modes:
            deploy_mode = "网一级部署模式"
            mark_content += "部署模式未成功识别，已默认设为：" + deploy_mode + "。"
        if "应用" in project_name:
            application_type = "应用"
        elif "平台" in project_name:
            application_type = "平台"
        elif "系统" in project_name:
            application_type = "系统"
        elif "工具" in project_name:
            application_type = "工具"
        else:
            application_type = "系统"  # 默认值

        # 获取原始内容
        raw_content = docx_search(username, project_name, '系统部署方式及软硬件资源需求')
        if raw_content:
            # 遍历content，识别到部署方式四个字以后开始保存，识别到软硬件资源需求时跳出循环
            for i in range(len(raw_content)):
                if "部署方式" in raw_content[i]['content']:
                    for j in range(i+1, len(raw_content)):
                        if "软硬件资源需求" in raw_content[j]['content']:
                            break
                        deployment_method.append(raw_content[j])
                    break
        
        # 若不存在则先判断新增还是改造然后分别套模板
        if not deployment_method:
            # 调用大模型识别出项目类型
            content = ""
            background = docx_search(username, project_name, "项目背景", "string")
            if background:
                content += background
            if content:
                try:
                    project_type = chat_with_llm(identify_type_prompt + content)
                except Exception as e:
                    logger.error(f"识别项目类型出错，错误信息：{e}")
                    project_type = "是"
                if not project_type:
                    project_type = "是"
                if "是" in project_type:
                    project_type = "新建项目"
                else:
                    project_type = "升级改造项目"
                mark_content += "项目类型识别为：" + project_type + "，请确认相关内容。"
            else:
                project_type = "新建项目"
                mark_content += "项目类型未成功识别，已默认设为：" + project_type + "。"

            if project_type == "新建项目":
                # 根据项目类型获取对应内容
                image_url = STATIC_URL + "template/deployment/new_project_0622.png"
                content_1 = """本应用（/平台/系统/工具）采用网一级部署模式（/网省两级部署模式/省一级部署模式/省地两级部署模式/省地县三级部署模式/网一级管理节点和省一级分节点部署模式），软硬件及应用部署模式必须支持全网用户上线后高可靠（例如JADP应用服务、中间件、数据库等）、高可用、强扩展性、大并发等应用需求，并能够支持IPV6网络环境或具备IPV6自动适配能力，部署结构图如下所示："""
                content_2 = """本应用（/平台/系统/工具）部署在南网云上，基于公司南网云平台，云化、微服务化的技术路线开展建设。"""
            else:
                image_url = STATIC_URL + "template/deployment/update_project_0622.png"
                content_2 = ""
                # 判断是否涉及软硬件资源需求和域名资源
                soft_flag = False
                url_flag = False
                for content in raw_content:
                    if "软硬件资源" in content['content']:
                        soft_flag = True
                    if "域名资源" in content['content']:
                        url_flag = True
                if soft_flag and url_flag:
                    content_1 = """本项目基于现行网一级部署模式（/网省两级部署模式/省一级部署模式/省地两级部署模式/省地县三级部署模式/网一级管理节点和省一级分节点部署模式）基础上，增加了软硬件资源需求，并确保软硬件及应用部署模式必须支持全网用户上线后高可靠（例如JADP应用服务、中间件、数据库等）、高可用、强扩展性、大并发等应用需求，并能够支持IPV6网络环境或具备IPV6自动适配能力，以及对域名资源进行了（/部分）调整。"""
                elif soft_flag and not url_flag:
                    content_1 = """本项目不涉及域名资源调整，基于现行网一级部署模式（/网省两级部署模式/省一级部署模式/省地两级部署模式/省地县三级部署模式/网一级管理节点和省一级分节点部署模式）基础上，增加了软硬件资源需求，并确保软硬件及应用部署模式必须支持全网用户上线后高可靠（例如JADP应用服务、中间件、数据库等）、高可用、强扩展性、大并发等应用需求，并能够支持IPV6网络环境或具备IPV6自动适配能力。"""
                else:
                    content_1 = """本项目无软硬件资源新增需求，继续沿用上一版本系统的软硬件资源和域名资源等，适当调整软硬件及应用部署模式以确保软硬件及应用部署模式必须支持全网用户上线后高可靠（例如JADP应用服务、中间件、数据库等）、高可用、强扩展性、大并发等应用需求，并能够支持IPV6网络环境或具备IPV6自动适配能力。"""
                    mark_content += "因无新增软硬件资源需求，未对软硬件资源需求进行调整。"
            
            # 构建回答
            deployment_method.append({
                "style": "-5",
                "content": mark_content
            })
            content_1 = content_1.replace("网一级部署模式（/网省两级部署模式/省一级部署模式/省地两级部署模式/省地县三级部署模式/网一级管理节点和省一级分节点部署模式）", deploy_mode).replace("应用（/平台/系统/工具）", application_type)
            deployment_method.append({
                "style": "0",
                "content": content_1
            })
            deployment_method.append({
                "style": "-1",
                "content": image_url
            })
            deployment_method.append({
                "style": "-2",
                "content": "（样例）"
            })
            if content_2:
                content_2 = content_2.replace("应用（/平台/系统/工具）", application_type)
                deployment_method.append({
                    "style": "0",
                    "content": content_2
                })

        return APIResponse(200, "生成部署方式成功", deployment_method)
    except Exception as e:
        logger.error(f"生成部署方式失败：{e}")
        return APIResponse(500, "生成部署方式失败", str(e))


@router.post("/system_deployment/hardware_software_requirements", summary="生成系统部署方式及软硬件资源需求-软硬件资源需求")
async def generate_deployment_method(request: ProjectInfoRequest):
    """
    生成系统部署方式及软硬件资源需求-软硬件资源需求。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **hardware_software_requirements**: 生成的软硬件资源需求内容

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        deployment_method = docx_search(
            request.username, request.project_name, "软硬件资源需求")
        if not deployment_method:
            return APIResponse(200, "资料中无软硬件资源需求内容", [
                {
                    "style": "0",
                    "content": "资料中未识别到软硬件资源需求内容！"
                },
                {
                    "style": "-5",
                    "content": "请提供软硬件资源需求相关内容！"
                }
            ])
        else:
            return APIResponse(200, "生成软硬件资源需求成功", deployment_method)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/security_technology_solution", summary="生成安全技术方案")
@api_response
async def generate_security_technology_solution(system_level_request: SystemLevelRequest):
    """
    生成安全技术方案。

    Parameters:
    - **system_level_request**: SystemLevelRequest 对象，包含用户名和项目名称

    Returns:
    - **security_technology_solution**: 生成的安全技术方案内容

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        security_technology_solution = docx_search(
            system_level_request.username, system_level_request.project_name, '安全技术方案')

        # 如果查找失败则读取指定文件
        if security_technology_solution is None:
            # 根据系统级别选择相应的文件
            if system_level_request.system_level in ["二级（含互联网用户）", "二级（不含互联网用户）", "一级（不含互联网用户）", "一级（含互联网用户）"]:
                file_name = '安全技术方案（二级信息系统）.txt'
            elif system_level_request.system_level in ["三级", "四级"]:
                file_name = '安全技术方案（三级信息系统）.txt'
            else:
                file_name = '安全技术方案（三级信息系统）.txt'
            content = read_document(file_name)
            security_technology_solution = parse_document(content)

        return APIResponse(200, "生成安全技术方案成功", security_technology_solution)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/application_architecture/application_module", summary="应用架构-应用模块")
@api_response
async def application_module(request: ProjectInfoRequest):
    """
    识别厂家资料，生成给定的应用架构-应用模块。
    返回应用模块描述文本和相关图片的url以及相关提示文本。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **results**: 生成的项目应用架构-应用模块内容

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        results = docx_search(
            request.username, request.project_name, '应用模块')
        if not results:
            return APIResponse(
                200,
                "未找到应用模块文本",
                [{
                    "style": "0",
                    "content": "资料中未识别到应用模块三级标题内容！"
                },
                    {
                    "style": "-5",
                    "content": "请提供应用模块相关内容！"
                }]
            )

        # 初始化
        flag = False
        domain_path = os.path.join(
            PROJECT_ROOT, 'documents/tugraph/entity/016-应用域.csv')
        standard_domain = list(pd.read_csv(domain_path)['业务域标准名称'])
        table_list = []
        origin_domain = []
        results_list = []

        # 验证是否存在应用模块内容，若存在则整合相关文本描述，若不存在则添加批注文本
        for content in results:
            # 若为正文文本，则检查是否包含了相关应用域名称的文本
            if int(content['style']) >= 0:
                for domain in standard_domain:
                    if domain in content['content']:
                        origin_domain.append(domain)
                        flag = True

            # 若为模块图片及其图注，则直接返回
            elif int(content['style']) >= -2:
                results_list.append(content)
                results_list.append({
                    'style': '0',
                    'content': '在应用架构图中选取所要实现的应用模块的信息，按照应用架构制品清单中的《AA-01.应用模块清单》的形式进行描述。'
                })

            # 若为表格，且表头为指定内容，则先将其保存为表格列表
            elif content['style'] == '-3':
                if content['content'][0] == [
                    "应用域编号",
                    "应用域名称",
                    "应用组编号",
                    "应用组名称",
                    "一级应用模块编号",
                    "一级应用模块名称",
                    "二级应用模块编号",
                    "二级应用模块名称",
                    "建设状态（建设中/待立项）"
                ]:
                    table_list.append(content['content'])

        # 若存在应用模块表格，整理生成文本描述及表格清单
        if table_list:
            merged_table = []
            for table in table_list:
                if not merged_table:
                    merged_table.append(table[0])
                merged_table.extend(table[1:])
            # 转换为Pandas DataFrame
            df = pd.DataFrame(merged_table[1:], columns=merged_table[0])
            # 提取并去重所需字段
            unique_app_domains = df["应用域名称"].unique().tolist()
            if unique_app_domains:
                origin_domain.extend(unique_app_domains)
                origin_domain = list(set(origin_domain))
                result_domain = []
                # 匹配最相近的一项应用域名称
                for domain in origin_domain:
                    closest_match = difflib.get_close_matches(
                        domain, standard_domain, n=1, cutoff=0.0)
                    if closest_match:
                        result_domain.append(closest_match[0])
            if result_domain:
                result_domain = set(list(result_domain))
                flag = True
            unique_app_groups = df["应用组名称"].unique().tolist()
            unique_first_level_modules = df["一级应用模块名称"].unique().tolist()
            unique_second_level_modules = df["二级应用模块名称"].unique().tolist()
            # 统计建设状态
            status_counts = df["建设状态（建设中/待立项）"].value_counts().to_dict()
            # 生成描述性文本
            app_domains_str = "、".join(origin_domain)
            app_groups_str = "、".join(unique_app_groups)
            if len(unique_first_level_modules) > 5:
                first_level_modules_str = "、".join(
                    unique_first_level_modules[:4])
            else:
                first_level_modules_str = "、".join(unique_first_level_modules)
            if len(unique_second_level_modules) > 5:
                second_level_modules_str = "、".join(
                    unique_second_level_modules[:4])
            else:
                second_level_modules_str = "、".join(
                    unique_second_level_modules)
            construction_count = status_counts.get("建设中", 0)
            planning_count = status_counts.get("待立项", 0)
            description = (
                f"本项目涉及域为{app_domains_str}等，涉及应用组为{app_groups_str}等，"
                f"涉及一级应用模块为：{first_level_modules_str}等，涉及二级应用模块为：{second_level_modules_str}等，"
                f"其中建设中{construction_count}项，待立项{planning_count}项。"
            )
            # 添加结果
            results_list.extend([{
                'style': '0',
                'content': description
            }, {
                'style': '0',
                'content': "本项目应用模块清单如下所示："
            }, {
                'style': '-3',
                'content': merged_table
            }])
        if not flag:
            results_list.append(
                {"style": "-5", "content": "应用模块内容中未识别到相关应用域文本，请检查文本内容！"})
        return APIResponse(
            200,
            "生成应用模块成功",
            results_list
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/application_architecture/application_functionality", summary="应用架构-应用功能")
@api_response
async def application_functionality(request: ProjectInfoRequest):
    """
    识别厂家资料，生成给定的应用架构-应用功能。
    返回应用功能描述文本和相关图片的url以及相关提示文本。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **results**: 生成的项目应用架构-应用功能内容

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    username = request.username
    project_name = request.project_name
    result = []
    try:
        # 获取原始内容
        origin_results = docx_search(username, project_name, '应用功能')
        flag = False
        if origin_results:
            result.extend(origin_results)
            for content in origin_results:
                if int(content['style']) > 3:
                    flag = True
                    break
        else:
            result = []
        if not flag:
            # 查找excel文件中的应用功能部分
            raw_df = read_app_func(username, project_name)
            if raw_df is None:
                if raw_df is None:
                    raw_df = read_app_func(username, project_name,
                                        sheet_name="表三附1（开发、集成工作量测算） ", start_pos=(13, 1))
                if raw_df is None:
                    raw_df = read_app_func(username, project_name,
                                        sheet_name="表三附1（开发、集成工作量测算）", start_pos=(13, 1))
            if raw_df is None:
                result.append({'style': '0', 'content': "未找到应用功能具体文本"})
                result.append({'style': '-5', 'content': "请提供应用功能相关内容！"})
                return APIResponse(200, "未找到应用功能文本", result)
            else:
                col_list = ['序号', '应用组', '一级应用模块', '二级应用模块',
                            '功能项', '功能子项', '功能描述']
                len_col = len(col_list)
                # 检查列数是否超过col_list的长度，若不足则用空字符串补齐，列名为对应的col_list
                len_raw = raw_df.shape[1]
                if len_raw < len_col:
                    len_add = len_col - len_raw
                    # 添加空字符串列
                    for i in range(len_add):
                        column_name = col_list[len_raw + i]
                        raw_df[column_name] = ''
                # 检查列名是否包含所需列，若存在则按照列名提取，若不存在则直接按对应序号提取
                df = pd.DataFrame()
                for i in range(len_col):
                    col = col_list[i]
                    if col in raw_df.columns:
                        # 检查raw_df[col]列中是否存在空值，若存在则填充空字符串
                        for j in range(len(raw_df[col])):
                            if pd.isna(raw_df.loc[j, col]):
                                raw_df.loc[j, col] = ''
                        df[col] = raw_df[col]
                    else:
                        # 若功能项不存在则读取第四列
                        if col == '功能项':
                            df[col] = raw_df.iloc[:, 3]
                        elif col == '二级应用模块':
                            df[col] = raw_df.iloc[:, 2]
                        else:
                            df[col] = raw_df.iloc[:, i]

                # 去除col_list对应列中的换行符和"/"或者"-"
                for col in col_list:
                    df.loc[:, col] = df[col].str.replace(
                        "\n", "").str.replace("/", "").str.replace("-", "")
                # 去除功能描述为空、"/"、"-"的行
                df = df[(df['功能描述'] != "") & (
                    df['功能描述'] != "/") & (df['功能描述'] != "-")]
                # 去除序号列
                df = df.drop(columns=['序号'])
                # 去除应用组包含交互、集成或者总计的行
                df = df[~df['应用组'].str.contains('交互|集成|总计')]
                col_list = ['应用组', '一级应用模块', '二级应用模块', '功能项', '功能子项']
                # 按照指定列依次进行排序，重置索引
                df = df.sort_values(by=col_list).reset_index(drop=True)

                # 遍历df，按次序生成文本描述
                result = origin_results if origin_results else []
                app_group, first_module, second_module, func_item, func_sub_item = "", "", "", "", ""
                for i in range(len(df)):
                    row = df.iloc[i]
                    if row['应用组'] != app_group:
                        app_group = row['应用组']
                        result.append({'style': '4', 'content': app_group})
                    if row['一级应用模块'] != first_module:
                        first_module = row['一级应用模块']
                        result.append({'style': '5', 'content': first_module})
                    if row['二级应用模块'] != second_module:
                        second_module = row['二级应用模块']
                        result.append({'style': '6', 'content': second_module})
                    if row['功能项'] != func_item:
                        func_item = row['功能项']
                        result.append({'style': '7', 'content': func_item})
                    if row['功能子项'] != func_sub_item:
                        func_sub_item = row['功能子项']
                        if func_sub_item not in ["", "/"]:
                            result.append(
                                {'style': '8', 'content': func_sub_item})
                    result.append({'style': '0', 'content': row['功能描述']})

                return APIResponse(
                    200,
                    "生成应用功能成功",
                    result
                )
        '''
        # 初始化以插入图谱验证的备注
        results = []
        label_list = [
            '应用组',
            '应用模块',
            '功能项',
            '功能子项',
        ]
        judge_flag = 10
        for i in range(len(origin_results)):
            results.append(origin_results[i])
            if int(origin_results[i]['style']) > 0:
                # 若不超过判断标记，则进行判断
                if int(origin_results[i]['style']) <= judge_flag:
                    flag = False
                    for label in label_list:
                        if not flag:
                            flag = validate_node(
                                origin_results[i]['content'], label)
                    # 若不在实体表中，则更新为当前样式
                    if not flag:
                        results.append(
                            {'style': '-5', 'content': '图谱中未查找到相关应用或功能！'})
                        judge_flag = int(origin_results[i]['style'])
                    # 若在实体表中，则继续往下判断
                    else:
                        judge_flag = 10
        # 判断是否为功能描述的部分，即上一个样式为7或8
        if i > 1 and origin_results[i]['style'] == '0' and origin_results[i-1]['style'] in ['7', '8']:
            # 若为功能描述，则添加功能描述的改进意见，并重置判断条件
            judge_flag = 10
            try:
                llm_data = chat_with_llm(identify_five_points_prompt + origin_results[i]['content'])
                llm_data = json.loads(llm_data)
                if llm_data['description']:
                    results.append(
                        {'style': '-5', 'content': llm_data['description']})
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))'''

        return APIResponse(
            200,
            "生成应用功能成功",
            result
        )
    except Exception as e:
        logger.error(f"生成应用功能失败: {e}")
        return APIResponse(500, "生成应用功能失败", [{"style": "0", "content": "请确认应用功能相关内容是否符合规范！"}])


# - 生成应用架构-应用交互
@router.post("/application_architecture/application_interaction", summary="应用架构-应用交互")
@api_response
async def application_interaction(request: ProjectInfoRequest):
    """
    识别厂家资料，生成给定的应用架构-应用交互。
    返回应用交互描述文本和相关图片的url以及相关提示文本。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **results**: 生成的项目应用架构-应用交互内容

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        # 获取原始内容
        origin_contents = docx_search(
            request.username, request.project_name, '应用交互')
        origin_table = sheet_search(
            request.username, request.project_name, '应用交互'
        )
        # 若原文档中无相关描述，且未找到对应工作簿，则返回提示批注
        if not origin_contents and not origin_table:
            return APIResponse(
                200,
                "未找到应用交互文本",
                [{
                    "style": "0",
                    "content": "资料中未识别到应用交互相关内容！"
                },
                    {
                    "style": "-5",
                    "content": "请提供应用交互相关内容！"
                }]
            )

        # 初始化结果列表，读取功能项实体表、功能项集成关系表
        results = []
        table_list = []
        '''function_path = os.path.join(
            PROJECT_ROOT, 'documents/tugraph/entity/019-功能项.csv')
        function_list = list(pd.read_csv(function_path)['name'])
        intergration_path = os.path.join(
            PROJECT_ROOT, 'documents/tugraph/relation/032-功能项-功能项.csv')
        intergration_data = pd.read_csv(intergration_path)
        '''
        for content in origin_contents:
            # 若为文本内容，则判断其是否在已有图谱中，对功能项及其子项进行提示批注
            if int(content['style']) >= 0:
                results.append(content)
                '''if int(content['style']) > 0:
                    mark_txt = ""
                    if content['content'] not in function_list:
                        mark_txt = '图谱中未查找到该功能项！'
                    else:
                        relation_list = find_relation(
                            intergration_data, content['content'])
                        if not relation_list:
                            mark_txt = '未查找到该功能项的集成关系！'
                        else:
                            for relation in relation_list:
                                mark_txt += f'输入功能项：{relation[0]}，输出功能项：{relation[1]}。\n'
                    results.append({'style': '-5', 'content': mark_txt})'''
            # 若为图片内容，则直接添加
            elif int(content['style']) >= -2:
                results.append(content)
            # 若为表格内容，则判断是否包含集成关系表格
            elif int(content['style']) == -3:
                if content['content'][0] == [
                    "应用模块活动协作编号",
                    "输入应用域",
                    "输入应用组",
                    "输入一级应用模块",
                    "输入二级应用模块",
                    "输入功能项",
                    "输入功能子项（可选）",
                    "输出应用域",
                    "输出应用组",
                    "输出一级应用模块",
                    "输出二级应用模块",
                    "概念实体",
                    "应用协作描述"
                ]:
                    table_list.append(content['content'])

        if table_list:
            merged_table = []
            for table in table_list:
                if not merged_table:
                    merged_table.append(table[0])
                merged_table.extend(table[1:])
            results.append({
                'style': '-3',
                'content': merged_table
            })
        elif origin_table:
            results.append({
                'style': '-3',
                'content': origin_table
            })
        else:
            results.append({
                'style': '-5',
                'content': "未识别到应用交互表格内容，请提交相关内容！"
            })
        return APIResponse(
            200,
            "生成应用交互成功",
            results
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/application_architecture/business_application_relationship", summary="应用架构-业务与应用对应情况")
@api_response
async def business_application_relationship(request: ProjectInfoRequest):
    """
    识别厂家资料，生成给定的应用架构-业务与应用对应情况。
    返回业务与应用对应情况以及相关提示文本。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **results**: 生成的项目应用架构-业务与应用对应情况

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        # 获取原始内容
        origin_contents = docx_search(
            request.username, request.project_name, '业务与应用对应情况')
        if not origin_contents:
            return APIResponse(
                200,
                "未找到业务与应用对应情况相关内容",
                [{
                    "style": "0",
                    "content": "资料中未识别到业务与应用对应情况三级标题内容！"
                },
                    {
                    "style": "-5",
                    "content": "请提供业务与应用对应情况相关内容！"
                }]
            )
        result_contents = []
        table_list = []
        for content in origin_contents:
            if content['style'] in ['-1', '-2']:
                result_contents.append(content)
            if int(content['style']) == -3:
                if content['content'][0] == [
                    "业务域",
                    "一级业务分类",
                    "二级业务分类",
                    "业务能力",
                    "业务流程",
                    "业务步骤编号",
                    "业务步骤",
                    "应用域",
                    "应用组",
                    "一级应用模块",
                    "二级应用模块",
                    "功能项",
                    "功能子项"
                ]:
                    table_list.append(content['content'])
        if table_list:
            merged_table = []
            for table in table_list:
                if not merged_table:
                    merged_table.append(table[0])
                merged_table.extend(table[1:])
            result_contents.extend([{
                'style': '0',
                'content': '本项目业务与应用对应情况如下所示：'
            }, {
                'style': '-3',
                'content': merged_table
            }])
        else:
            result_contents = origin_contents
        return APIResponse(
            200,
            "生成业务与应用对应情况成功",
            result_contents
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/implementation_requirements/implementation_strategy", summary="生成项目实施需求-实施策略")
@api_response
async def generate_implementation_strategy(request: ProjectInfoRequest):
    """
    生成项目实施需求-实施策略。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **implementation_strategy**: 生成的项目实施需求-实施策略文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        # 尝试从文档中查找内容
        implementation_strategy = docx_search(
            request.username, request.project_name, '实施策略')

        # 若未找到内容，则读取默认文件并转换为指定格式的列表
        if not implementation_strategy:
            content = read_document('实施策略.txt')
            implementation_strategy = [{"style": "0", "content": content}]

        return APIResponse(200, "生成实施策略成功", implementation_strategy)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/implementation_requirements/implementation_plan", summary="生成项目实施需求-实施计划")
@api_response
async def generate_implementation_plan(request: ProjectInfoRequest):
    """
    生成项目实施需求-实施计划。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **implementation_plan**: 生成的项目实施需求-实施计划文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    content_list = []
    try:
        # 读取config文件中的开发费用
        config = read_config(request.username, request.project_name)
        if config:
            dev_fee = config.get('dev_fee', 0)
        else:
            dev_fee = 350
        if dev_fee <= 200:
            plan_path = "小于等于200万.csv"
            content = "本项目预计于下达启动之日起分阶段、分步骤开展项目开发与实施工作。整个项目周期计划共7个月（其中建设周期为4个月），本项目初步实施进度计划如下表所示： "
        elif 200 < dev_fee <= 500:
            plan_path = "200万至500万.csv"
            content = "本项目预计于下达启动之日起分阶段、分步骤开展项目开发与实施工作。整个项目周期计划共9个月（其中建设周期为6个月），本项目初步实施进度计划如下表所示： "
        elif 500 < dev_fee <= 1000:
            plan_path = "500万至1000万.csv"
            content = "本项目预计于下达启动之日起分阶段、分步骤开展项目开发与实施工作。整个项目周期计划共11个月（其中建设周期为8个月），本项目初步实施进度计划如下表所示： "
        elif 1000 < dev_fee <= 2000:
            plan_path = "1000万至2000万.csv"
            content = "本项目预计于下达启动之日起分阶段、分步骤开展项目开发与实施工作。整个项目周期计划共13个月（其中建设周期为10个月），本项目初步实施进度计划如下表所示： "
        elif 2000 < dev_fee <= 5000:
            plan_path = "2000万至5000万.csv"
            content = "本项目预计于下达启动之日起分阶段、分步骤开展项目开发与实施工作。整个项目周期计划共15个月（其中建设周期为12个月），本项目初步实施进度计划如下表所示： "
        elif 5000 < dev_fee <= 10000:
            plan_path = "5000万至10000万.csv"
            content = "本项目预计于下达启动之日起分阶段、分步骤开展项目开发与实施工作。整个项目周期计划共19个月（其中建设周期为16个月），本项目初步实施进度计划如下表所示： "
        else:
            plan_path = "大于10000万.csv"
            content = "本项目预计于下达启动之日起分阶段、分步骤开展项目开发与实施工作。整个项目周期计划共24个月（其中建设周期为21个月），本项目初步实施进度计划如下表所示： "
        content_list.append({
            "stlye": "0",
            "content": content
        })

        plan_path = os.path.join(PROJECT_ROOT, "documents", "实施计划", plan_path)
        # 读取为二维表格
        plan_table = pd.read_csv(plan_path, header=None)
        # 转换为列表格式
        plan_list = plan_table.values.tolist()
        content_list.append({
            "style": "-3",
            "content": plan_list
        })

        return APIResponse(200, "生成实施计划成功", content_list)
    except Exception as e:
        logger.error(f"生成实施计划失败：{e}")
        return APIResponse(500, "生成实施计划失败", [{
            "style": "0",
            "content": "资料中未识别到实施计划相关内容！"
        }, {
            "style": "-5",
            "content": "请提供实施计划相关内容！"
        }])


@router.post("/implementation_requirements/implementation_task_decomposition", summary="生成项目实施需求-实施任务分解")
@api_response
async def generate_implementation_task_decomposition(request: ProjectInfoRequest):
    """
    生成项目实施需求-实施任务分解。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **implementation_plan**: 生成的项目实施需求-实施任务分解文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        content_list = []
        config = read_config(request.username, request.project_name)
        construction_unit = '广东电网公司'
        dev_fee = 350
        if config:
            construction_unit = config.get('construction_unit', '广东电网公司')
            dev_fee = config.get('dev_fee', 0)
        unit_list = construction_unit.split('、')
        if len(unit_list) == 1:
            csv_file = os.path.join(
                PROJECT_ROOT, "documents", "实施任务分配", "表三附2（实施工作量）-单公司_0409.csv")
        else:
            if dev_fee <= 200:
                sheet = "表三附2(实施工作量)-开发费小于等于200万.csv"
            elif 200 < dev_fee <= 500:
                sheet = "表三附2(实施工作量)-开发费200万至500万.csv"
            elif 500 < dev_fee <= 1000:
                sheet = "表三附2(实施工作量)-开发费500万至1000万.csv"
            elif 1000 < dev_fee <= 2000:
                sheet = "表三附2(实施工作量)-开发费1000万至2000万.csv"
            elif 2000 < dev_fee <= 5000:
                sheet = "表三附2(实施工作量)-开发费2000万至5000万.csv"
            else:
                sheet = "表三附2(实施工作量)-开发费5000万至10000万.csv"
            csv_file = os.path.join(PROJECT_ROOT, "documents", "实施任务分配", sheet)
        content_df = pd.read_csv(csv_file, header=None)
        content_df = content_df.fillna('')
        content = content_df.values.tolist()
        content_list.append({
            "style": "-3",
            "content": content
        })
        return APIResponse(200, "生成实施任务分解成功", content_list)
    except Exception as e:
        logger.error(f"生成实施任务分解失败：{e}")
        return APIResponse(500, "生成实施任务分解失败", [{
            "style": "0",
            "content": "资料中未识别到实施任务分解相关内容！"
        }, {
            "style": "-5",
            "content": "请提供实施任务分解相关内容！"
        }])
