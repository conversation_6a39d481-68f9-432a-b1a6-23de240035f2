# --coding:utf-8--
# 本文件用于存放以下接口：
# - 检查厂家资料 /check_word

from fastapi import APIRouter, HTTPException
from app.utils.response import APIResponse, api_response
from app.docs_api.schemas import ProjectInfoRequest
from app.utils.doc_processor import docx_search
from app.core.logging_config import get_logger

router = APIRouter()
logger = get_logger(__name__)


def check_third_chapter(username, project_name, third_title):
    """
    检查三级标题是否存在缺失
    Args:
        username (str): 用户名
        project_name (str): 项目名称
        third_title (str): 三级标题
    Returns:
        content_list (list): 检查结果
    """
    content_list = []
    content_list.append({"style": "3", "content": third_title})
    try:
        # 检查三级标题
        chapter_content = docx_search(username, project_name, third_title)
        # 三级标题不存在则添加备注然后直接返回，若存在则添加所有内容
        if not chapter_content:
            content_list.append(
                {"style": "-5", "content": f"{third_title}章节缺失，请检查相关内容！"})
        else:
            content_list = content_list + chapter_content
    except Exception as e:
        logger.error(f"检查三级标题{third_title}失败：{e}")
        content_list.append({"style": "-5", "content": f"{third_title}检查失败！"})
    finally:
        return content_list


def check_second_chapter(username, project_name, second_title):
    """
    检查二级标题是否存在缺失
    Args:
        username (str): 用户名
        project_name (str): 项目名称
        second_title (str): 二级标题
    Returns:
        content_list (list): 检查结果
    """
    content_list = []
    try:
        # 检查二级标题
        content_list.append({"style": "2", "content": second_title})
        chapter_content = docx_search(username, project_name, second_title)
        # 二级标题不存在则添加备注然后直接返回，若存在则检查下一级小标题
        if not chapter_content:
            content_list.append(
                {"style": "-5", "content": f"{second_title}章节缺失，请检查相关内容！"})
        else:
            for content in chapter_content:
                if int(content["style"]) >= 2:
                    break
                content_list.append(content)
            # 若二级标题为需求分析等，则需要检查三级标题
            third_list = []
            if second_title == "需求分析":
                third_list = ["业务需求", "功能需求", "非功能需求"]
            elif second_title == "业务架构":
                third_list = ["业务能力", "业务流程", "业务流程协作情况"]
            elif second_title == "应用架构":
                third_list = ["应用模块", "应用功能", "应用交互", "业务与应用对应情况"]
            elif second_title == "数据架构":
                third_list = ["数据域", "逻辑实体", "逻辑实体分布"]
            elif second_title == "技术架构":
                third_list = ["技术架构分类"]
            elif second_title == "项目实施需求":
                third_list = ["实施策略", "实施计划"]
            else:
                for content in chapter_content:
                    if int(content["style"]) == 3:
                        third_list.append(content["content"])
            for third_title in third_list:
                content_list = content_list + \
                    check_third_chapter(username, project_name, third_title)

    except Exception as e:
        logger.error(f"检查二级标题{second_title}失败：{e}")
        content_list.append({"style": "-5", "content": f"{second_title}检查失败！"})
    return content_list


def check_first_chapter(username, project_name, first_title):
    """
    检查一级标题是否存在缺失
    Args:
        username (str): 用户名
        project_name (str): 项目名称
        first_title (str): 一级标题
    Returns:
        content_list (list): 检查结果
    """
    content_list = []
    try:
        # 检查第一章
        content_list.append({"style": "1", "content": first_title})
        chapter_content = docx_search(username, project_name, first_title)
        # 第一章不存在则添加备注然后直接返回，若存在则检查二级标题
        if not chapter_content:
            content_list.append(
                {"style": "-5", "content": f"{first_title}章节缺失，请检查相关内容！"})
        else:
            for content in chapter_content:
                if int(content["style"]) >= 1:
                    break
                content_list.append(content)
            second_list = []
            if first_title == "概述":
                second_list = ["项目背景", "项目依据", "项目目标", "项目范围"]
            elif first_title == "项目现状及必要性分析":
                second_list = ["现状分析", "需求分析", "必要性结论"]
            elif first_title == "项目方案":
                second_list = ["业务架构", "应用架构", "数据架构", "技术架构",
                               "系统部署方式及软硬件资源需求", "等级保护需求", "项目实施需求"]
            elif first_title == "项目效益分析":
                second_list = ["管理效益分析", "经济效益分析", "社会效益分析"]
            else:
                for content in chapter_content:
                    if int(content["style"]) == 2:
                        second_list.append(content["content"])
            for second_title in second_list:
                content_list = content_list + \
                    check_second_chapter(username, project_name, second_title)

    except Exception as e:
        logger.error(f"检查一级标题{first_title}失败：{e}")
        content_list.append({"style": "-5", "content": f"{first_title}检查失败！"})
    finally:
        return content_list


@router.post("/check_word", summary="检查word")
@api_response
async def check_word(request: ProjectInfoRequest):
    """
    检查厂家资料的word

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含以下字段:
        - username (str): 用户名
        - project_name (str): 项目名称

    Returns:
    - **APIResponse**: 包含以下字段:
        - code (int): 状态码, 200表示成功
        - message (str): 响应消息
        - data (list): 字典列表，字典包含以下字段:
            - style (str): 段落样式
            - content (str): 段落内容
    Raises:
    - **HTTPException(500)**: 服务器内部错误
    """
    first_list = ["概述", "项目现状及必要性分析", "项目方案", "项目效益分析"]
    result_content = []
    try:
        for first_title in first_list:
            result_content = result_content + \
                check_first_chapter(
                    request.username, request.project_name, first_title)
        return APIResponse(status_code=200, message="检查word成功", data=result_content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检查word厂家资料失败: {e}")
    

def gen_third_chapter(username, project_name, third_title, count):
    """
    检查三级标题是否存在缺失
    Args:
        username (str): 用户名
        project_name (str): 项目名称
        third_title (str): 三级标题
        count (list): 记录一级、二级、三级标题缺失数量
    Returns:
        content_list (list): 检查结果
    """
    content_list = []
    try:
        chapter_content = docx_search(username, project_name, third_title)
        # 三级标题不存在则返回True，若存在则返回False
        if not chapter_content:
            content_list.append(
                {"missing_flag": True, "content": "        " + third_title})
            count[2] += 1
        else:
            content_list.append(
                {"missing_flag": False, "content": "        " + third_title})
    except Exception as e:
        logger.error(f"检查三级标题{third_title}失败：{e}")
        content_list.append(
            {"missing_flag": True, "content": "        " + third_title})
        count[2] += 1
    finally:
        return content_list


def gen_second_chapter(username, project_name, second_title, count):
    """
    检查二级标题是否存在缺失
    Args:
        username (str): 用户名
        project_name (str): 项目名称
        second_title (str): 二级标题
        count (list): 记录一级、二级、三级标题缺失数量
    Returns:
        content_list (list): 检查结果
    """
    content_list = []
    try:
        chapter_content = docx_search(username, project_name, second_title)
        # 二级标题不存在则添加备注然后直接返回，若存在则检查下一级小标题
        if not chapter_content:
            content_list.append(
                {"missing_flag": True, "content": "    " + second_title})
            count[1] += 1
        else:
            content_list.append(
                {"missing_flag": False, "content": "    " + second_title})
            # 若二级标题为需求分析等，则需要检查三级标题
            third_list = []
            if second_title == "需求分析":
                third_list = ["业务需求", "功能需求", "非功能需求"]
            elif second_title == "业务架构":
                third_list = ["业务能力", "业务流程", "业务流程协作情况"]
            elif second_title == "应用架构":
                third_list = ["应用模块", "应用功能", "应用交互", "业务与应用对应情况"]
            elif second_title == "数据架构":
                third_list = ["数据域", "逻辑实体", "逻辑实体分布"]
            elif second_title == "技术架构":
                third_list = ["技术架构分类"]
            elif second_title == "项目实施需求":
                third_list = ["实施策略", "实施计划"]
            else:
                for content in chapter_content:
                    if int(content["style"]) == 3:
                        third_list.append(content["content"])
            for third_title in third_list:
                content_list = content_list + \
                    gen_third_chapter(username, project_name, third_title, count)

    except Exception as e:
        logger.error(f"检查二级标题{second_title}失败：{e}")
        content_list.append(
            {"missing_flag": True, "content": "    " + second_title})
        count[1] += 1
    return content_list


def gen_first_chapter(username, project_name, first_title, count):
    """
    检查一级标题是否存在缺失
    Args:
        username (str): 用户名
        project_name (str): 项目名称
        first_title (str): 一级标题
        count (list): 记录一级、二级、三级标题缺失数量
    Returns:
        content_list (list): 检查结果
    """
    content_list = []
    try:
        chapter_content = docx_search(username, project_name, first_title)
        # 第一章不存在则添加备注然后直接返回，若存在则检查二级标题
        if not chapter_content:
            content_list.append(
                {"missing_flag": True, "content": first_title})
            count[0] += 1
        else:
            content_list.append(
                {"missing_flag": False, "content": first_title})
            second_list = []
            if first_title == "概述":
                second_list = ["项目背景", "项目依据", "项目目标", "项目范围"]
            elif first_title == "项目现状及必要性分析":
                second_list = ["现状分析", "需求分析", "必要性结论"]
            elif first_title == "项目方案":
                second_list = ["业务架构", "应用架构", "数据架构", "技术架构",
                               "系统部署方式及软硬件资源需求", "等级保护需求", "项目实施需求"]
            elif first_title == "项目效益分析":
                second_list = ["管理效益分析", "经济效益分析", "社会效益分析"]
            else:
                for content in chapter_content:
                    if int(content["style"]) == 2:
                        second_list.append(content["content"])
            for second_title in second_list:
                content_list = content_list + \
                    gen_second_chapter(username, project_name, second_title, count)

    except Exception as e:
        logger.error(f"检查一级标题{first_title}失败：{e}")
        content_list.append({"missing_flag": True, "content": first_title})
        count[0] += 1
    finally:
        return content_list


@router.post("/gen_report", summary="生成检查报告")
@api_response
async def gen_report(request: ProjectInfoRequest):
    """
    生成检查结果报告

    Args:
    - **request**: ProjectInfoRequest 对象，包含以下字段:
        - username (str): 用户名
        - project_name (str): 项目名称

    Returns:
    - **APIResponse**: 包含以下字段:
        - code (int): 状态码, 200表示成功
        - message (str): 响应消息
        - data (list): 字典列表，字典包含以下字段:
            - missing_flag (str): 是否缺失
            - content (str): 段落内容
    Raises:
    - **HTTPException(500)**: 服务器内部错误
    """
    result_content = []
    content = []
    first_list = ["概述", "项目现状及必要性分析", "项目方案", "项目效益分析"]
    count = [0, 0, 0]  # 记录一级、二级、三级标题缺失数量
    try:
        for first_title in first_list:
            content = content + \
                gen_first_chapter(request.username, request.project_name, first_title, count)
        summary = "检查结果："
        if count == [0, 0, 0]:
            summary += "所有章节均存在，无缺失。"
        else:
            if count[0] > 0:
                summary += f"一级标题缺失{count[0]}个，"
            else:
                summary += "一级标题完整，"
            if count[1] > 0:
                summary += f"二级标题缺失{count[1]}个，"
            else:
                summary += "二级标题完整，"
            if count[2] > 0:
                summary += f"三级标题缺失{count[2]}个。"
            else:
                summary += "三级标题完整。"
        result_content = [{"missing_flag": False, "content": summary}] + content
        return APIResponse(status_code=200, message="生成检查报告成功", data=result_content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成检查报告失败: {e}")
