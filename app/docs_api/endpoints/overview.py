# 本文件用于存放以下接口：
# - 生成项目背景 /background
# - 生成项目依据 /basis
# - 生成项目目标 /goals
# - 生成项目范围-业务范围 /project_scope/business_scope
# - 生成项目范围-投资（建设）单位 /project_scope/investment_unit
# - 生成项目范围-应用范围 /project_scope/application_scope
# - 生成项目范围-开发范围 /project_scope/development_scope

# 导入相关依赖
import os
import json
import pandas as pd
from app.core.logging_config import get_logger
import re
from app.core.config import settings
from app.utils.materials import search_task
from fastapi import APIRouter, HTTPException
from app.utils.doc_processor import docx_search
from app.utils.llm import chat_with_llm, process_text
from app.utils.response import APIResponse, api_response, RESPONSE_MODELS
from app.schemas import SuccessResponse, ErrorResponse, DocumentResponse
from app.docs_api.endpoints.cover import parse_project_name
from app.templates.prompt_templates import background_generate_prompt, objective_generate_prompt, polish_development_scope_prompt, generate_development_scope_prompt, identify_project_type_prompt
from app.docs_api.schemas import ProjectInfoRequest, InvestmentUnit, InvestmentUnit

router = APIRouter()
logger = get_logger(__name__)

# 从配置中获取上传目录
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY
STATIC_URL = settings.STATIC_URL


def get_project_info(project_name):
    '''
    获取给定的项目基本信息。

    Parameters:
    - **project_name (str)**: 项目名称，包含项目类（项目名称）

    Returns:
    - **project_info (str)**: 项目基本信息
    '''
    try:
        project_info = "项目名称：" + project_name + "。"
        project_type, _ = parse_project_name(project_name)
        project_path = os.path.join(
            settings.PROJECT_ROOT, 'documents/tugraph/entity/004-项目类.csv')
        project_df = pd.read_csv(project_path)
        contents = project_df[project_df['项目类'] == project_type]
        content_list = contents.to_dict(orient='records')
        content = content_list[0]['建设内容']
        project_info += "项目建设内容：" + content
        task_measure = search_task(project_type)
        project_info += "项目涉及的规划举措：" + task_measure['规划举措'] + \
            "；项目涉及的重要规划任务：" + task_measure['规划任务名称'] + \
            "；规划建设目标：" + task_measure['建设目标'] + \
            "规划总体要求：" + task_measure['总体要求']
        return project_info
    except Exception as e:
        logger.error(f"获取项目基本信息出错：{e}", exc_info=True)
        return None


@router.post(
    "/background",
    summary="生成项目背景",
    description="基于厂家资料和项目信息智能生成项目背景内容",
    responses={
        200: {"model": DocumentResponse, "description": "项目背景生成成功"},
        500: {"model": ErrorResponse, "description": "生成失败或服务器错误"}
    },
    tags=["第一章 概述"]
)
@api_response
async def background(request: ProjectInfoRequest):
    """
    ## 生成项目背景

    基于上传的厂家资料和项目基础信息，智能生成项目背景内容。
    系统会优先使用厂家资料中的项目背景信息，如果没有则基于项目分类信息生成。

    ### 功能特性
    - 智能内容提取和生成
    - 基于项目分类的背景信息补充
    - AI文本优化和润色
    - 格式标准化处理

    ### 生成逻辑
    1. 从厂家资料中搜索"项目背景"相关内容
    2. 如果未找到，则基于项目类型生成基础背景信息
    3. 使用AI模型对内容进行优化和润色
    4. 格式化输出，去除多余换行

    ### 数据来源
    - **厂家资料**: 上传的Word文档中的项目背景章节
    - **项目库**: 基于项目分类的标准背景模板
    - **规划信息**: 相关规划举措和建设目标

    ### 参数说明
    - **username**: 用户名，用于定位用户项目目录
    - **project_name**: 项目名称，格式为"项目类（具体项目名称）"

    ### 返回格式
    返回包含样式信息的文档内容列表，样式"0"表示正文内容。

    ### 应用场景
    - 可研报告第一章概述部分
    - 项目申报材料背景说明
    - 技术方案背景介绍

    ### 错误处理
    - **500**: AI服务调用失败、文件读取错误或其他系统异常
    """
    try:
        raw_background = docx_search(
            request.username, request.project_name, '项目背景', format='string')
        if not raw_background:
            raw_background = get_project_info(request.project_name)
        background = chat_with_llm(
            background_generate_prompt+raw_background).strip()
        background = background.replace('\n\n', '\n').strip()
        return APIResponse(200, "生成项目背景成功", [{"style": "0", "content": background}])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/basis", summary="项目依据")
@api_response
async def basis_generate(request: ProjectInfoRequest):
    """
    生成项目依据。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **basis**: 项目依据文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        basis = docx_search(request.username, request.project_name, '项目依据')
        if basis:
            return APIResponse(200, "生成项目依据成功", basis)
        else:
            # 读取项目依据模板
            project_basis_template_path = os.path.join(
                settings.PROJECT_ROOT, 'documents', '项目依据模板.json')
            if not os.path.exists(project_basis_template_path):
                raise HTTPException(
                    status_code=404, detail="文件 '项目依据模板.json' 不存在")
            with open(project_basis_template_path, 'r', encoding='utf-8') as file:
                project_basis_template = json.load(file)

            # 读取config文件获取项目类型
            config_path = os.path.join(UPLOAD_DIRECTORY, request.username, request.project_name, 'config.json')
            if not os.path.exists(config_path):
                config = {"project_category": "信息系统建设与升级改造"}
            else:
                with open(config_path, 'r', encoding='utf-8') as file:
                    config = json.load(file)
            if 'project_category' not in config:
                config['project_category'] = "信息系统建设与升级改造"
            project_type = config['project_category']

            if project_type == "信息系统建设与升级改造":
                basis = project_basis_template["信息系统建设与升级改造"]
                business_demand = docx_search(
                    request.username, request.project_name, '业务需求', format='string')
                if business_demand:
                    if "人工智能" in business_demand or "智能" in business_demand:
                        basis.extend(project_basis_template["人工智能"])
                    if "云计算" in business_demand:
                        basis.extend(project_basis_template["云计算"])
                    if "物联网" in business_demand:
                        basis.extend(project_basis_template["物联网"])
                    if "敏捷开发" in business_demand:
                        basis.extend(project_basis_template["敏捷开发"])
                    if "移动应用" in business_demand:
                        basis.extend(project_basis_template["移动应用"])
            elif project_type == "信息基础设施建设与升级改造":
                basis = project_basis_template["信息基础设施建设与升级改造"]
            elif project_type == "信息安全防护体系建设与升级改造":
                basis = project_basis_template["信息安全防护体系建设与升级改造"]
            elif project_type == "运行维护":
                basis = project_basis_template["运行维护"]
            elif project_type == "信息专题研究":
                basis = project_basis_template["信息专题研究"]

            result = [{"style": "0", "content": project_basis_template["开头"]}]
            result.extend([{"style": "-6", "content": item} for item in basis])
            return APIResponse(200, "生成项目依据成功", result)
    except Exception as e:
        logger.error(f"生成项目依据出错：{e}")
        return APIResponse(500, "生成项目依据失败", data=str(e))


@router.post("/goals", summary="生成项目目标")
@api_response
async def goals(request: ProjectInfoRequest):
    """
    生成给定的项目目标。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **goals**: 项目目标文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        raw_goals = docx_search(
            request.username, request.project_name, '项目目标', format='string')
        if not raw_goals:
            background = docx_search(
                request.username, request.project_name, '项目背景', format='string')
            if not background:
                background = get_project_info(request.project_name)
            goals = chat_with_llm(objective_generate_prompt + background)
        else:
            goals = process_text(raw_goals, option="polish")
        goals = goals.replace('\n\n', '\n').strip()
        return APIResponse(200, "生成项目目标成功", [{"style": "0", "content": goals}])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/project_scope/business_scope", summary="生成项目业务范围")
@api_response
async def business_scope(request: ProjectInfoRequest):
    """
    生成给定的项目业务范围。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **business**: 项目业务范围

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        raw_scope = docx_search(
            request.username, request.project_name, '业务范围', format='string')
        if not raw_scope:
            raw_scope = docx_search(
                request.username, request.project_name, '项目范围', format='string')
        if not raw_scope:
            business_path = os.path.join(
                settings.PROJECT_ROOT, "documents/tugraph/entity/005-业务域.csv")
            business_df = pd.read_csv(business_path)
            business_list = list(business_df['name'])
            business_txt = docx_search(
                request.username, request.project_name, "业务架构", format="string")
            scope_list = []
            for business in business_list:
                if business in business_txt:
                    scope_list.append(business)
            raw_scope = '、'.join(list(set(scope_list)))
            raw_scope = '本项目业务范围涉及' + raw_scope + "等域。"
        return APIResponse(200, "生成项目业务范围成功", [{"style": "0", "content": raw_scope}])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


def read_investment_units():
    """
    读取投资（建设）单位，并解析为字典形式。

    Returns:
    - **单位字典**: {简称: 全称}
    """
    file_path = os.path.join(settings.PROJECT_ROOT,
                             'documents', '公司简称-全称对应表.json')

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件 '公司简称-全称对应表.json' 不存在")

    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            unit_dict = json.load(file)
        return unit_dict
    except json.JSONDecodeError as e:
        raise HTTPException(status_code=500, detail=f"JSON文件解析错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取文件时发生错误: {str(e)}")


class InvestmentUnitStore:
    # 存储投资单位的类
    def __init__(self):
        self.investment_unit = None

    def set_investment_unit(self, unit: str, username: str, project_name: str):
        # 使用配置中的上传目录
        folder_path = os.path.join(UPLOAD_DIRECTORY, username, project_name)
        os.makedirs(folder_path, exist_ok=True)  # 确保文件夹存在

        # 构建文件路径
        file_path = os.path.join(folder_path, 'investment_unit.json')

        # 存储投资单位到文件
        with open(file_path, "w", encoding="utf-8") as file:
            json.dump({"investment_unit": unit}, file)

    def get_investment_unit(self, username: str, project_name: str):
        # 使用配置中的上传目录
        file_path = os.path.join(
            UPLOAD_DIRECTORY, username, project_name, 'investment_unit.json')

        if not os.path.exists(file_path):
            return None  # 文件不存在时返回 None

        # 从文件中读取投资单位
        with open(file_path, "r", encoding="utf-8") as file:
            data = json.load(file)
            return data.get("investment_unit", None)


# 创建一个实例用于存储投资单位
investment_unit_store = InvestmentUnitStore()


@router.post("/project_scope/investment_unit", summary="生成投资（建设）单位")
@api_response
async def generate_investment_unit(data: InvestmentUnit):
    """
    根据公司简称生成投资（建设）单位全称，并存储到文件。

    Parameters:
    - **data**: InvestmentUnit 对象，包含公司简称列表

    Returns:
    - **investment_unit**: 投资（建设）单位全称字符串

    Raises:
    - **HTTPException(400)**: 输入为空或无效
    """
    if not data.units:
        raise HTTPException(status_code=400, detail="输入不能为空")
    # 获取单位字典
    unit_dict = read_investment_units()

    selected_units = []
    for item in data.units:
        if item in unit_dict:
            selected_units.append(unit_dict[item])
        else:
            raise HTTPException(status_code=400, detail=f"输入的简称 '{item}' 无效")

    # 将全称拼接为字符串
    investment_unit = "、".join(selected_units)

    # 存储生成的投资单位到文件
    investment_unit_store.set_investment_unit(
        investment_unit, data.username, data.project_name)

    result = f"该项目投资（建设）单位为{investment_unit}。"

    return APIResponse(200, "生成投资（建设）单位成功", {"investment_unit": result})


@router.get("/project_scope/investment_unit", summary="获取存储的投资（建设）单位")
@api_response
async def get_investment_unit(username: str, project_name: str):
    """
    获取存储的投资（建设）单位全称。

    Returns:
    - **investment_unit**: 投资（建设）单位全称字符串
    """
    investment_unit = investment_unit_store.get_investment_unit(
        username, project_name)
    if not investment_unit:
        raise HTTPException(status_code=404, detail="未找到存储的投资（建设）单位")

    return APIResponse(200, "获取投资（建设）单位成功", {"investment_unit": investment_unit})


# 生成应用范围
@router.post("/project_scope/application_scope", summary="生成应用范围")
@api_response
async def get_application_scope(data: InvestmentUnit):
    """
    生成应用范围。

    Parameters:
    - **data**: ApplicationScope 对象，包含单位名称

    Returns:
    - **application_scope**: 应用范围单位列表

    Raises:
    - **HTTPException(400)**: 无效的应用范围
    """
    if not data.units:
        raise HTTPException(status_code=400, detail="输入不能为空")  # 输入为空时抛出400错误
    valid_units = read_investment_units()

    # 根据输入查找全称
    selected_units = []
    for item in data.units:
        if item in valid_units:
            selected_units.append(valid_units[item])  # 添加全称
        else:
            raise HTTPException(
                status_code=400, detail=f"输入的简称 '{item}' 无效")  # 无效输入时抛出400错误

    application_scope = "、 ".join(selected_units)
    # 返回格式化的字符串

    result = f"该项目应用范围为{application_scope}。"

    return APIResponse(200, "生成应用范围成功", {"application_scope": result})


# 生成开发范围
@router.post("/project_scope/development_scope", summary="生成开发范围")
@api_response
async def scope_generate(request: ProjectInfoRequest):
    """
    生成给定的开发范围。

    Parameters:
    - **request**: ProjectInfoRequest 对象，包含用户名和项目名称

    Returns:
    - **scope**: 开发范围文本

    Raises:
    - **HTTPException(500)**: 其他未预期的错误
    """
    try:
        result_text = docx_search(
            request.username, request.project_name, '开发范围')
        # 如果开发范围为空，则先根据项目范围获取原始内容
        if not result_text:
            result_text = docx_search(
                request.username, request.project_name, '项目范围')
            for con_dict in result_text:
                if con_dict['style'] == '0' and "开发范围" in con_dict['content']:
                    result_text = con_dict['content']
                    break

        # 如果开发范围为空，则根据"项目目标"和"功能需求"生成
        if not result_text:
            target_text = docx_search(
                request.username, request.project_name, '项目目标', format='string')
            demand_text = docx_search(
                request.username, request.project_name, '功能需求', format='string')

            context = ""
            if target_text:
                context += "项目目标：" + target_text + "\n"
            if demand_text:
                context += "功能需求：" + demand_text + "\n"

            if not context:  # 实在找不到参考内容
                return APIResponse(200, "缺乏足够信息生成开发范围", [{"style": "0", "content": "缺乏足够信息生成开发范围"}])

            # 调用大模型生成开发范围
            result_text = chat_with_llm(
                generate_development_scope_prompt + context)
            result_text = re.sub(r"\n+", "\n", result_text)
            result_text = result_text.replace('\n\n', '\n').strip()
            return APIResponse(200, "生成开发范围成功", [{"style": "0", "content": result_text}])
        else:
            # 若识别出开发范围，则进行润色
            polished_text = chat_with_llm(
                polish_development_scope_prompt + result_text)
            polished_text = polished_text.replace('\n\n', '\n').strip()
            return APIResponse(200, "识别出开发范围并润色成功", [{"style": "0", "content": polished_text}])

    except Exception as e:
        logger.error(f"开发范围生成失败：{e}")
        return APIResponse(500, "开发范围生成失败", [{
                "style": "0", "content": "资料中未识别到开发范围相关内容！"
            }, {
                "style": "-5", "content": "请提供开发范围相关内容"
            }])
