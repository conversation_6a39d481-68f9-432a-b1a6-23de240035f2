from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.common.router import router as common_router
from app.docs_api.router import router as docs_router
from app.excel_api.router import router as excel_router
from app.admin_api.router import router as admin_api_router
from app.admin_api.endpoints.admin import create_admin_user
from app.frontend_version.router import router as frontend_version_router
from fastapi.staticfiles import StaticFiles
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
import os
from app.core.config import settings
from app.utils.database import DatabaseManager
from contextlib import asynccontextmanager
from fastapi.responses import HTMLResponse, FileResponse, FileResponse
from app.core.logging_config import setup_logging, get_logger


# 初始化日志系统
setup_logging(app_mode=settings.APP_MODE, log_dir=settings.LOG_DIR)
logger = get_logger(__name__)

# 读取APP_MODE环境变量，如果为prod，则禁用fastapi 文档
env = os.getenv("APP_MODE", "dev")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    logger.info("应用启动中...")
    db = DatabaseManager(
        host=settings.DB_HOST,
        port=settings.DB_PORT,
        user=settings.DB_USER,
        password=settings.DB_PASSWORD,
        database=settings.DB_NAME
    )
    try:
        db.connect()
        await create_admin_user(db)
        logger.info("管理员账户创建/验证完成")
    except Exception as e:
        logger.error(f"创建管理员账户失败: {str(e)}", exc_info=True)
    finally:
        db.disconnect()

    logger.info("应用启动完成")
    yield
    # 关闭时执行
    logger.info("应用关闭中...")
    logger.info("应用已关闭")

# OpenAPI 元数据配置
openapi_metadata = {
    "title": "有解助手后端接口文档",
    "description": """
## 有解助手后端API服务


### 🎯 主要功能模块

- **文档生成**: 智能生成项目可研报告等文档
- **表格计算**: 自动生成各类项目估算表格

### 🔧 技术特性

- 基于FastAPI框架构建，提供高性能RESTful API
- 集成大语言模型，支持智能文本处理和生成
- 支持多种文档格式解析和数据提取
- 提供完整的错误处理和日志记录
- 符合OpenAPI 3.0规范，便于集成和测试

### 📋 API分类

- **通用接口**: 文件操作、用户认证、AI文本处理
- **文档接口**: 项目文档各章节内容生成
- **表格接口**: 各类估算表格和计算表生成
- **版本接口**: 前端版本管理和下载服务
    """,
    "version": "1.0.0",
    "contact": {
        "name": "有解助手开发团队",
        "email": "<EMAIL>"
    },
    "license_info": {
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT"
    },
    "servers": [
        {
            "url": "http://***************:12010",
            "description": "开发环境"
        },
        {
            "url": "http://*************:12010",
            "description": "生产环境"
        }
    ],
    "openapi_url": "/openapi.json",
    "docs_url": None,
    "redoc_url": None,
    "lifespan": lifespan
}

if env == "prod":
    # 生产环境配置
    app = FastAPI(**openapi_metadata)
else:
    # 开发环境配置
    app = FastAPI(**openapi_metadata)


def custom_openapi():
    """自定义OpenAPI schema生成，增强标签和分类信息"""
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
        servers=openapi_metadata["servers"]
    )

    # 添加详细的标签信息
    openapi_schema["tags"] = [
        {
            "name": "files",
            "description": "📁 文件管理 - 文档上传、解析、存储和管理功能"
        },
        {
            "name": "auth",
            "description": "🔐 用户认证 - 用户注册、登录、令牌验证和权限管理"
        },
        {
            "name": "llm_chat",
            "description": "🤖 AI文本处理 - 基于大语言模型的文本扩写、缩写和优化"
        },
        {
            "name": "封面",
            "description": "📄 文档封面 - 项目封面信息生成和管理"
        },
        {
            "name": "第一章 概述",
            "description": "📖 项目概述 - 项目背景、目标、范围等基础信息生成"
        },
        {
            "name": "第二章 项目现状及必要性分析",
            "description": "📊 现状分析 - 项目现状调研和建设必要性分析"
        },
        {
            "name": "第三章 项目方案",
            "description": "🎯 技术方案 - 项目技术架构、实施方案和部署策略"
        },
        {
            "name": "第四章 项目投资估算",
            "description": "💰 投资估算 - 项目成本分析和投资预算计算"
        },
        {
            "name": "第五章 项目效益分析",
            "description": "📈 效益分析 - 项目经济效益和社会效益评估"
        },
        {
            "name": "第六章 项目风险分析",
            "description": "⚠️ 风险评估 - 项目风险识别、分析和应对措施"
        },
        {
            "name": "第七章 项目可研结论",
            "description": "✅ 可研结论 - 项目可行性研究总结和建议"
        },
        {
            "name": "检查Word厂家资料",
            "description": "🔍 资料检查 - Word文档内容验证和质量检查"
        },
        {
            "name": "封面、编制说明",
            "description": "📋 表格封面 - Excel表格封面和编制说明生成"
        },
        {
            "name": "表一（项目估算汇总表）",
            "description": "📊 汇总表 - 项目总体投资估算汇总"
        },
        {
            "name": "表二（项目分项汇总投资表）",
            "description": "📈 分项汇总 - 按分项统计的投资汇总表"
        },
        {
            "name": "表三（项目分项估算表）",
            "description": "📋 分项估算 - 详细的项目分项估算明细"
        },
        {
            "name": "表四（其他费用估算表）",
            "description": "💸 其他费用 - 项目其他相关费用估算"
        },
        {
            "name": "表五（分阶段投资估算表）",
            "description": "⏱️ 阶段投资 - 按实施阶段划分的投资计划"
        },
        {
            "name": "表六（软件设备汇总表）、表七（设备服务租赁汇总表）、表八（需求变更费用对比表）",
            "description": "🖥️ 设备服务 - 软硬件设备和服务租赁相关表格"
        },
        {
            "name": "表三附1（开发、集成工作量测算）",
            "description": "⚙️ 工作量测算 - 开发和系统集成工作量评估"
        },
        {
            "name": "表三附2（实施工作量）",
            "description": "🔧 实施工作量 - 项目实施阶段工作量估算"
        },
        {
            "name": "计算参数表",
            "description": "🧮 参数配置 - 估算计算所需的各类参数设置"
        },
        {
            "name": "生成分摊单位表格",
            "description": "🏢 分摊单位 - 投资分摊单位信息表格生成"
        },
        {
            "name": "检查Excel厂家资料",
            "description": "✅ Excel检查 - Excel文档内容验证和数据校验"
        },
        {
            "name": "admin-users",
            "description": "👥 用户管理 - 系统用户的增删改查和权限管理"
        },
        {
            "name": "admin-projects",
            "description": "📁 项目管理 - 项目信息管理和状态跟踪"
        },
        {
            "name": "admin-auth",
            "description": "🔑 管理员认证 - 管理员身份验证和权限控制"
        },
        {
            "name": "frontend_version",
            "description": "🔄 版本管理 - 前端应用版本控制和分发管理"
        }
    ]

    # 添加组件信息
    if "components" not in openapi_schema:
        openapi_schema["components"] = {}

    # 设置安全方案
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "JWT令牌认证，格式：Bearer <token>"
        }
    }

    app.openapi_schema = openapi_schema
    return app.openapi_schema


# 应用自定义OpenAPI schema
app.openapi = custom_openapi




# 添加前端静态文件支持
swagger_ui_dir = os.path.join(settings.DOCS_STATIC_DIR, "swagger-ui")
app.mount("/swagger-ui", StaticFiles(directory=swagger_ui_dir), name="swagger-ui")

# 挂载downloads页面静态资源
if os.path.exists(settings.DOWNLOADS_STATIC_DIR):
    app.mount("/downloads-static", StaticFiles(directory=settings.DOWNLOADS_STATIC_DIR), name="downloads-static")

# 挂载公共前端资源
if os.path.exists(settings.COMMON_STATIC_DIR):
    app.mount("/frontend-common", StaticFiles(directory=settings.COMMON_STATIC_DIR), name="frontend-common")


@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    # 创建自定义的 swagger ui 路由
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        swagger_js_url="/swagger-ui/swagger-ui-bundle.js",
        swagger_css_url="/swagger-ui/swagger-ui.css"
    )


app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 获取项目根目录的静态文件夹路径
static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static")

# 挂载静态文件
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# 挂载管理中心前端静态文件
if os.path.exists(settings.ADMIN_DIST_DIR):
    app.mount("/admin", StaticFiles(directory=settings.ADMIN_DIST_DIR, html=True), name="admin-frontend")

# 挂载前端公共资源
app.mount("/frontend-static", StaticFiles(directory=settings.FRONTEND_RESOURCES_ROOT), name="frontend-static")

app.include_router(common_router, prefix="/common")

app.include_router(docs_router, prefix="/docs_api")

app.include_router(excel_router, prefix="/excel_api")

# 添加管理后台API路由
app.include_router(admin_api_router, prefix="/admin_api")

# 旧版管理后台UI路由已移除，现在使用独立的React前端

# 添加前端版本管理路由
app.include_router(frontend_version_router, prefix="/frontend")

# 添加下载页面路由
from app.frontend_version.endpoints.version import version_history_page
app.get("/downloads", response_class=HTMLResponse)(version_history_page)

@app.get("/favicon.ico", include_in_schema=False)
async def favicon():
    favicon_path = os.path.join(settings.COMMON_STATIC_DIR, "favicon.ico")
    return FileResponse(favicon_path)

@app.get("/favicon.ico", include_in_schema=False)
async def favicon():
    """返回网站图标"""
    favicon_path = os.path.join(settings.COMMON_STATIC_DIR, "favicon.ico")
    if os.path.exists(favicon_path):
        return FileResponse(favicon_path)
    else:
        # 如果新位置不存在，回退到旧位置
        old_favicon_path = os.path.join(settings.PROJECT_ROOT, "static", "favicon.ico")
        return FileResponse(old_favicon_path)

@app.get("/")
async def root():
    logger.info("Root endpoint accessed")
    return {"message": "Welcome to the FastAPI project"}
