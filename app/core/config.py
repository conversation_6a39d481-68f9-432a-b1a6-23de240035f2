from pydantic_settings import BaseSettings
import os
import yaml
from typing import List, Dict, Any


class Settings(BaseSettings):
    # 项目根目录
    PROJECT_ROOT: str = os.path.dirname(os.path.dirname(
        os.path.dirname(os.path.abspath(__file__))))

    # 上传文件设置
    UPLOAD_DIRECTORY: str = os.environ['UPLOAD_DIRECTORY']  # 上传文件目录
    MAX_FILE_SIZE: int = int(os.environ['MAX_FILE_SIZE'])  # 最大文件大小

    # 加密用户token所需密钥
    SECRET_KEY: str = os.environ['SECRET_KEY']

    # 数据库设置 - 从环境变量获取
    DB_HOST: str = os.environ['DB_HOST']    # 数据库主机
    DB_PORT: int = int(os.environ['DB_PORT'])  # 数据库端口
    DB_USER: str = os.environ['DB_USER']      # 数据库用户名
    DB_PASSWORD: str = os.environ['DB_PASSWORD']  # 数据库密码
    DB_NAME: str = os.environ['DB_NAME']      # 数据库名称

    # 图数据库设置
    GDB_HOST: str = os.environ['GDB_HOST']    # 图数据库主机
    GDB_USER: str = os.environ['GDB_USER']    # 图数据库用户名
    GDB_PASSWORD: str = os.environ['GDB_PASSWORD']  # 图数据库密码

    # LLM配置
    LLM_CONFIGURATIONS_RAW: str = os.environ.get(
        'LLM_CONFIGURATIONS_RAW', '[]')
    LLM_CONFIGURATIONS: List[Dict[str, Any]] = []

    # 静态文件访问URL前缀
    STATIC_URL: str = os.environ['STATIC_URL']  # 静态文件访问URL前缀

    # 前端资源配置
    FRONTEND_RESOURCES_ROOT: str = os.path.join(PROJECT_ROOT, "frontend-resources")
    FRONTEND_PACKAGES_DIR: str = os.path.join(FRONTEND_RESOURCES_ROOT, "packages")
    ADMIN_SOURCE_DIR: str = os.path.join(FRONTEND_RESOURCES_ROOT, "admin", "source")
    ADMIN_DIST_DIR: str = os.path.join(FRONTEND_RESOURCES_ROOT, "admin", "dist")
    DOWNLOADS_STATIC_DIR: str = os.path.join(FRONTEND_RESOURCES_ROOT, "downloads", "static")
    DOWNLOADS_TEMPLATES_DIR: str = os.path.join(FRONTEND_RESOURCES_ROOT, "downloads", "templates")
    DOCS_STATIC_DIR: str = os.path.join(FRONTEND_RESOURCES_ROOT, "docs")
    COMMON_STATIC_DIR: str = os.path.join(FRONTEND_RESOURCES_ROOT, "common")
    MAX_DOWNLOAD_SIZE: int = 100 * 1024 * 1024  # 100MB

    # 日志配置
    LOG_LEVEL: str = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_DIR: str = os.environ.get('LOG_DIR', 'logs')
    APP_MODE: str = os.environ.get('APP_MODE', 'dev')

    def __init__(self, **values: Any):
        super().__init__(**values)
        try:
            loaded_configs = yaml.safe_load(self.LLM_CONFIGURATIONS_RAW)
            if isinstance(loaded_configs, list):
                self.LLM_CONFIGURATIONS = loaded_configs
            else:
                # Handles cases where RAW is empty or malformed resulting in non-list (e.g. None)
                # 使用标准库logging，因为此时自定义日志系统可能还未初始化
                import logging
                logging.warning(
                    f"LLM_CONFIGURATIONS_RAW did not parse into a list: {self.LLM_CONFIGURATIONS_RAW}. Using empty list.")
                self.LLM_CONFIGURATIONS = []
        except yaml.YAMLError as e:
            import logging
            logging.warning(
                f"Could not parse LLM_CONFIGURATIONS_RAW as YAML: {e}. Using empty list. Value was: {self.LLM_CONFIGURATIONS_RAW}")
            self.LLM_CONFIGURATIONS = []
        except Exception as e:  # Catch any other unexpected errors during parsing
            import logging
            logging.error(
                f"An unexpected error occurred while parsing LLM_CONFIGURATIONS_RAW: {e}. Using empty list. Value was: {self.LLM_CONFIGURATIONS_RAW}")
            self.LLM_CONFIGURATIONS = []


settings = Settings()
