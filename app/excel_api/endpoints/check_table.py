# --coding:utf-8--
# 本文件用于存放以下接口：
# - 检查表三附1功能描述 /check_table

import os
import json
import numpy as np  # 添加numpy导入
from app.core.logging_config import get_logger
import pandas as pd
from app.utils.llm import extract_json, async_chat_with_llm, distribute_queries_to_apis
from fastapi import APIRouter, HTTPException
from app.core.config import settings
from app.docs_api.schemas import ExcelRequest
from app.docs_api.endpoints.cover import parse_project_name
from app.utils.doc_processor import docx_search
from app.utils.response import APIResponse, api_response
from app.templates.prompt_templates import mark_desc_prompt
from app.utils.excel_processor import read_app_func, get_start_end

# 从配置中获取上传目录
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY
LLM_PROCESSING_BATCH_SIZE = 64  # 批量处理的大小

logger = get_logger(__name__)

router = APIRouter()


def convert_paragraphs(paragraphs):
    """
    将段落列表转换为二维列表，处理标题和正文段落。
    Args:
        paragraphs (List[Dict]): 段落列表，每个段落是一个字典，包含 'style' 和 'content' 键。
    Returns:
        List[List[str]]: 转换后的二维列表，每行包含正文内容和对应的标题。
    Raises:
        ValueError: 如果段落列表为空或格式不正确。
    """
    try:
        result = []
        title_stack = []         # 当前各层级标题
        buffered_content = ''    # 正文缓存
        max_level_seen = 0       # 全局最大标题层级

        for para in paragraphs:
            level = int(para['style'])-3
            content = para['content']

            if level > 0:  # 是标题
                current_max_level = len(title_stack)

                if buffered_content:
                    # 判断新标题是否比当前标题栈的层级高
                    if level > current_max_level:
                        # 舍弃缓存内容，不清空标题栈，仅更新标题
                        buffered_content = ''
                    else:
                        # 将缓存正文写入结果
                        row = [buffered_content]
                        for i in range(max_level_seen):
                            if i < len(title_stack):
                                row.append(title_stack[i])
                            else:
                                row.append('')
                        result.append(row)
                        buffered_content = ''

                # 更新标题栈
                if level <= len(title_stack):
                    title_stack[level - 1] = content
                    title_stack = title_stack[:level]
                else:
                    while len(title_stack) < level - 1:
                        title_stack.append('')
                    title_stack.append(content)

                # 更新全局最大标题层级
                if level > max_level_seen:
                    max_level_seen = level

            elif level == -3:  # 是正文
                if buffered_content:
                    buffered_content += '\n' + content
                else:
                    buffered_content = content

        # 处理最后可能存在的缓存正文
        if buffered_content:
            row = [buffered_content]
            for i in range(max_level_seen):
                if i < len(title_stack):
                    row.append(title_stack[i])
                else:
                    row.append('')
            result.append(row)

        return result

    except Exception as e:
        logger.error(f"转换段落列表失败: {e}")
        return []


def process_2d_list(group_name, data):
    """
    处理二维列表，确保每行有五列：内容、一级标题、二级标题、三级标题、四级标题。
    Args:
        group_name (str): 应用组名称
        data (List[List[str]]): 二维列表，每行包含内容和标题。
    Returns:
        List[List[str]]: 处理后的二维列表，每行包含内容和四个标题。
    Raises:
        ValueError: 如果输入数据格式不正确或处理失败。
    """

    if not data:
        return []
    result = []

    try:
        for row in data:
            content = row[0]
            titles = row[1:]

            # 找到最后一个非空字符串的索引
            last_title_index = -1
            for i, title in enumerate(titles):
                if title.strip():
                    last_title_index = i

            # 判断是否有四级或以上标题
            has_deep_titles = last_title_index >= 3  # 索引3对应四级标题

            if has_deep_titles:
                # 保留一级、二级 + 截取最后两级标题作为三、四级
                new_title1 = titles[0] if len(titles) > 0 else ''
                new_title2 = titles[1] if len(titles) > 1 else ''
                new_title3 = titles[last_title_index -
                                    1] if last_title_index - 1 >= 0 else ''
                new_title4 = titles[last_title_index] if last_title_index >= 0 else ''
            else:
                # 少于四级标题的情况：使用已有标题，不足的复制最后一级非空标题
                title_values = [t for t in titles if t.strip()]
                last_title = title_values[-1] if title_values else ''

                new_title1 = title_values[0] if len(title_values) > 0 else ''
                new_title2 = title_values[1] if len(
                    title_values) > 1 else last_title
                new_title3 = title_values[2] if len(
                    title_values) > 2 else last_title
                new_title4 = last_title
            if new_title1:
                result.append(["", group_name, new_title1,
                              new_title2, new_title3, new_title4, content])

        return result

    except Exception as e:
        logger.error(f"处理二维列表失败: {e}")
        return []


def read_word_app(username, project_name) -> pd.DataFrame:
    """
    读取word厂家资料中的应用功能。
    Args:
        username (str): 用户名
        project_name (str): 项目名称
    Returns:
        app_df (pandas.DataFrame): 应用功能数据
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 读取word厂家资料
        # 获取原始内容
        source_path = os.path.join(UPLOAD_DIRECTORY, username, project_name)
        origin_results = docx_search(username, project_name, '应用功能')
        if not origin_results:
            raise HTTPException(status_code=404, detail="未找到word中应用功能相关资料！")
        # 初始化列名
        columns = ['序号', '应用组', '一级应用模块', '二级应用模块', '功能项', '功能子项', '功能描述']
        # 用于保存所有行的数据
        rows = []
        # 用于保存上一行的部分数据
        last_row_data = {}

        # 读取config文件获取建设单位
        construction_unit = "南方电网"
        config_path = os.path.join(source_path, 'config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as file:
                config_data = json.load(file)
                if config_data.get('construction_unit'):
                    construction_unit = config_data['construction_unit']

        if construction_unit != "广东电网公司":

            index = 1  # 序号初始化
            i = 0  # content列表索引初始化
            content = []
            for i in range(len(origin_results)):
                if origin_results[i]['style'] == '0':
                    origin_results[i]['style'] = '10'
                if int(origin_results[i]['style']) > 0:
                    content.append(origin_results[i])
            i = 0
            while i < len(content):
                if content[i]['style'] == '4':
                    last_row_data['应用组'] = content[i]['content']
                elif content[i]['style'] == '5':
                    last_row_data['一级应用模块'] = content[i]['content']
                elif content[i]['style'] == '6':
                    last_row_data['二级应用模块'] = content[i]['content']
                elif content[i]['style'] == '7':
                    last_row_data['功能项'] = content[i]['content']
                    i += 1
                    while i < len(content) and int(content[i]['style']) > 7:
                        if content[i]['style'] == '8':
                            # 若已经存在功能描述则先保存
                            if last_row_data.get('功能描述'):
                                rows.append(
                                    {**{'序号': index}, **last_row_data.copy()})
                                index += 1
                                last_row_data['功能描述'] = ""
                            last_row_data['功能子项'] = content[i]['content']
                            i += 1
                            while i < len(content) and int(content[i]['style']) > 8:
                                if content[i]['style'] == '10' and last_row_data.get('功能描述'):
                                    last_row_data['功能描述'] += '\n' + \
                                        content[i]['content']
                                elif content[i]['style'] == '10' and not last_row_data.get('功能描述'):
                                    last_row_data['功能描述'] = content[i]['content']
                                i += 1
                            if last_row_data.get('功能描述'):
                                rows.append(
                                    {**{'序号': index}, **last_row_data.copy()})
                                index += 1
                                last_row_data['功能描述'] = ""
                            continue
                        elif content[i]['style'] == '10' and last_row_data.get('功能描述'):
                            last_row_data['功能描述'] += '\n' + \
                                content[i]['content']
                        elif content[i]['style'] == '10' and not last_row_data.get('功能描述'):
                            last_row_data['功能子项'] = '/'
                            last_row_data['功能描述'] = content[i]['content']
                        i += 1
                    if last_row_data.get('功能描述'):
                        rows.append({**{'序号': index}, **last_row_data.copy()})
                        index += 1
                        last_row_data['功能描述'] = ""
                    continue
                i += 1

        else:
            _, group_name = parse_project_name(project_name)
            rows = convert_paragraphs(origin_results)
            rows = process_2d_list(group_name, rows)
            if not rows:
                raise HTTPException(
                    status_code=404, detail="未找到word中应用功能相关资料！")

        # 创建DataFrame并返回
        app_df = pd.DataFrame(rows, columns=columns)
        return app_df

    except Exception as e:
        logger.error(f"从Word中获取应用功能数据失败！{e}")
        return None


def read_excel_app(app_raw: pd.DataFrame) -> pd.DataFrame:
    """
    从excel获取应用功能部分数据。
    Args:
        app_raw (pandas.DataFrame): 原始数据
    Returns:
        app_df (pandas.DataFrame): 应用功能dataframe
    Raises:
        HTTPException(500): 服务器内部错误
    """
    # 初始化
    col_list = ['序号', '应用组', '一级应用模块', '二级应用模块', '功能项', '功能子项', '功能描述']
    app_df = pd.DataFrame(columns=col_list)
    try:
        len_col = len(col_list)
        # 检查列名是否包含所需列，若存在则按照列名提取，若不存在则直接按对应序号提取
        app_df = pd.DataFrame()
        for i in range(len_col):
            col = col_list[i]
            if col in app_raw.columns:
                # 检查raw_df[col]列中是否存在空值，若存在则填充空字符串
                for j in range(len(app_raw[col])):
                    if pd.isna(app_raw.loc[j, col]):
                        app_raw.loc[j, col] = ''
                app_df[col] = app_raw[col]
            else:
                # 若功能项不存在则读取第四列，功能子项不存在也读取第四列
                if col == '功能项':
                    app_df[col] = app_raw.iloc[:, 3]
                elif col == '功能子项':
                    app_df[col] = app_raw.iloc[:, 3]
                else:
                    app_df[col] = app_raw.iloc[:, i]
        # 遍历每一行，判断每个值是否为空值，若为空则填充为空字符串，若第二列的值为总计，则将该行及其之后的行全部删除
        for i in range(len(app_raw)):
            for j in range(len_col):
                if pd.isna(app_df.iloc[i, j]):
                    app_df.iloc[i, j] = ''
            if app_df.iloc[i, 1] == '总计':
                app_df = app_df.iloc[:i]
                break
    except Exception as e:
        logger.error(f"从Excel中获取应用功能数据失败！{e}")
    finally:
        return app_df


def read_word_inte(username, project_name) -> pd.DataFrame:
    """
    从word获取系统集成部分数据。
    Args:
        username (str): 用户名
        project_name (str): 项目名称
    Returns:
        inte_df (pandas.DataFrame): 系统集成dataframe
        inte_mark_list (List): 备注列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    # 创建DataFrame
    columns = ['序号', '应用组', '一级应用模块', '二级应用模块', '功能项', '功能子项', '功能描述']
    inte_raw_df = pd.DataFrame(columns=columns)
    try:
        # 初始化
        rows = []  # 用于保存所有行的数据
        last_row_data = {}  # 用于保存上一行的部分数据

        # 读取word并将正文style设置为10便于判断
        origin_results = docx_search(username, project_name, '应用交互')
        if not origin_results:
            raise HTTPException(status_code=404, detail="未找到word中应用交互相关资料！")
        content = []
        for i in range(len(origin_results)):
            if origin_results[i]['style'] == '0':
                origin_results[i]['style'] = '10'
            if int(origin_results[i]['style']) > 0:
                content.append(origin_results[i])

        # 序号初始化
        i = 0
        index = 1
        while i < len(content):
            if content[i]['style'] == '4':
                last_row_data['应用组'] = "系统集成"
                last_row_data['一级应用模块'] = project_name
                i += 1
            elif content[i]['style'] == '5':
                last_row_data['二级应用模块'] = content[i]['content']
                last_row_data['功能项'] = content[i]['content']
                last_row_data['功能子项'] = ''
                i += 1
                # 继续往下判断直到遇到更高级的标题或者内容结束，即往下一直读正文内容
                while i < len(content) and int(content[i]['style']) > 5:
                    # 若当前内容为正文，则判断是否已有功能描述，若没有则新增，有则续上
                    if content[i]['style'] == '10' and not last_row_data.get('功能描述'):
                        last_row_data['功能描述'] = content[i]['content']
                    elif content[i]['style'] == '10' and last_row_data.get('功能描述'):
                        last_row_data['功能描述'] += '\n' + content[i]['content']
                    i += 1
                # 判断是否已有功能描述，若有则保存
                if last_row_data.get('功能描述'):
                    rows.append({**{'序号': index}, **last_row_data.copy()})
                    index += 1
                    last_row_data['功能描述'] = ""
            else:
                i += 1

        inte_raw_df = pd.DataFrame(rows, columns=columns)

        return inte_raw_df

    except Exception as e:
        logger.error(f"从Word中获取系统集成数据失败！{e}")
        return inte_raw_df
    

def read_excel_inte(inte_raw: pd.DataFrame, project_name: str):
    """
    从excel获取系统集成部分数据。
    Args:
        inte_raw (pandas.DataFrame): 原始数据
        username (str): 用户名
        project_name (str): 项目名称
        start_pos (tuple): 起始位置，默认为(14, 1)
        app_df (pandas.DataFrame): 应用功能数据
    Returns:
        inte_df (pandas.DataFrame): 系统集成dataframe
        inte_mark_list (List): 备注列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    # 初始化
    _, group_name = parse_project_name(project_name)
    col_list = ['序号', '应用组', '一级应用模块', '二级应用模块', '功能项', '功能子项', '功能描述']
    inte_df = pd.DataFrame(columns=col_list)
    try:
        if inte_raw is None or inte_raw.empty:
            logger.warning("Excel中未找到系统集成部分数据，返回空DataFrame。")
            return inte_df
        raw_col = ['输入应用组', '输入一级应用模块', '输出应用组', '输出一级应用模块', '应用协作描述']
        inte_raw = inte_raw.loc[:, raw_col]
        inte_raw.dropna(inplace=True)
        # 对原始df中的每一行进行生成
        if inte_raw is not None and not inte_raw.empty:
            for row in range(len(inte_raw)):
                desc = inte_raw.iloc[row]['应用协作描述']
                if desc not in ["", "\\", "/", "-"]:
                    group_in = inte_raw.iloc[row]['输入应用组']
                    group_out = inte_raw.iloc[row]['输出应用组']
                    first_out = inte_raw.iloc[row]['输出一级应用模块']
                    # 判断输入输出应用组哪一个包含项目名称
                    if group_name in first_out:
                        second_app = "与" + group_in + "集成"
                    else:
                        second_app = "与" + group_out + "集成"
                    # 生成df
                    temp_df = pd.DataFrame(
                        [["", "系统集成", second_app, second_app, second_app, second_app, desc]], columns=col_list)
                    inte_df = pd.concat([inte_df, temp_df], ignore_index=True)
    except Exception as e:
        logger.error(f"从Excel中获取系统集成数据失败！{e}")
    finally:
        return inte_df


def cut_app_inte(raw_df: pd.DataFrame):
    """
    将应用功能部分和系统集成部分进行切割。
    Args:
        raw_df (pandas.DataFrame): 原始数据
    Returns:
        app_raw (pandas.DataFrame): 切割后的应用功能部分数据
        inte_raw (pandas.DataFrame): 切割后的系统集成部分数据
    """
    # 初始化
    app_raw = pd.DataFrame()
    inte_raw = pd.DataFrame()
    try:
        # 先截取原始数据中的对应列
        col_list = ['序号', '应用组', '一级应用模块', '二级应用模块', '功能项', '功能子项', '功能描述']

        # 检查列数是否超过col_list的长度，若不足则用空字符串补齐，列名为对应的col_list
        len_raw = raw_df.shape[1]
        len_col = len(col_list)
        if len_raw < len_col:
            len_add = len_col - len_raw
            # 添加空字符串列
            for i in range(len_add):
                column_name = col_list[len_raw + i]
                raw_df[column_name] = ''
        
        # 检查列名是否包含所需列，若存在则按照列名提取，若不存在则直接按对应序号提取
        app_df = pd.DataFrame()
        for i in range(len_col):
            col = col_list[i]
            if col in raw_df.columns:
                # 检查raw_df[col]列中是否存在空值，若存在则填充空字符串
                for j in range(len(raw_df[col])):
                    if pd.isna(raw_df.loc[j, col]):
                        raw_df.loc[j, col] = ''
                app_df[col] = raw_df[col]
            else:
                # 若功能项不存在则读取第四列
                if col == '功能项':
                    app_df[col] = raw_df.iloc[:, 3]
                else:
                    app_df[col] = raw_df.iloc[:, i]

        # 遍历每一行，若第三列中包含集成两个字，则将该行保存到inte_df中，否则保存到app_df中
        for i in range(len(raw_df)):
            # 去除原始数据中第二列为总计之后的行
            if raw_df.iloc[i, 2] == '总计':
                break
            if '集成' in str(raw_df.iloc[i, 2]):
                inte_raw = pd.concat([inte_raw, raw_df.iloc[[i]]], ignore_index=True)
            else:
                app_raw = pd.concat([app_raw, raw_df.iloc[[i]]], ignore_index=True)

        # 重置索引
        app_raw.reset_index(drop=True, inplace=True)
        inte_raw.reset_index(drop=True, inplace=True)

        return app_raw, inte_raw
    except Exception as e:
        logger.error(f"切割应用功能和系统集成部分失败！{e}")
        return None, None


async def mark_desc(raw_df):
    """
    使用大模型对功能描述进行评价。
    Args:
        raw_df (pandas.DataFrame): 应用功能数据
    Returns:
        raw_df (pandas.DataFrame): 生成描述后的应用功能数据
    """
    # 初始化列表
    mark_list = []
    try:

        # 1. 准备所有查询
        all_queries_with_indices = []
        for idx, row in raw_df.iterrows():
            func_desc = row['功能描述']
            all_queries_with_indices.append((idx, mark_desc_prompt + func_desc))
        
        parsed_llm_data_map = {}
        if all_queries_with_indices:
            logger.info(
                f"准备分批处理 {len(all_queries_with_indices)} 条有效的功能描述 (judge_eif)...")
            for i in range(0, len(all_queries_with_indices), LLM_PROCESSING_BATCH_SIZE):
                batch_queries_with_indices = all_queries_with_indices[i:i +
                                                                      LLM_PROCESSING_BATCH_SIZE]
                current_batch_queries = [item[1]
                                         for item in batch_queries_with_indices]
                current_batch_indices = [item[0]
                                         for item in batch_queries_with_indices]
                logger.info(
                    f"judge_eif: 正在处理批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}，包含 {len(current_batch_queries)} 条查询。")

                batch_llm_results_str = []
                try:  # Ensuring this try is correctly formatted
                    batch_llm_results_str = await distribute_queries_to_apis(current_batch_queries)
                    logger.info(
                        f"judge_eif (批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}): 成功从LLM批量获取 {len(batch_llm_results_str)} 条结果。")
                except Exception as e:  # Ensuring this except is aligned with the try
                    logger.error(
                        f"judge_eif (批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}): 调用 distribute_queries_to_apis 批量处理失败: {e}. 对该批次进行逐行回退处理。")
                    temp_results_for_fallback = []
                    for query_idx_in_batch, query_for_fallback in enumerate(current_batch_queries):
                        original_df_idx_for_fallback = current_batch_indices[query_idx_in_batch]
                        try:
                            single_res = await async_chat_with_llm(query_for_fallback)
                            temp_results_for_fallback.append(single_res)
                        except Exception as single_e:
                            logger.error(
                                f"Fallback (judge_eif, 批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}, 原始索引 {original_df_idx_for_fallback}): 单行处理失败. Error: {single_e}")
                            temp_results_for_fallback.append(json.dumps(
                                {"description": {"score": 0, "advices": f"调用大模型失败: {single_e}"}}))
                    batch_llm_results_str = temp_results_for_fallback

                for batch_item_idx, llm_data_str in enumerate(batch_llm_results_str):
                    original_df_idx = current_batch_indices[batch_item_idx]
                    try:
                        llm_answer = extract_json(llm_data_str)
                        if llm_answer == []:
                            logger.error(
                                f"解析来自LLM的结果失败 (原始索引 {original_df_idx}, 批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}): {llm_data_str[:200]}... 结果为空。")
                            parsed_llm_data_map[original_df_idx] = {
                                "description": {"score": 0, "advices": f"结果解析失败: {e}，大模型回答：{llm_data_str}"}}
                        else:
                            parsed_llm_data_map[original_df_idx] = llm_answer[0]
                    except Exception as e:
                        logger.error(
                            f"解析来自LLM的结果失败 (judge_eif, 原始索引 {original_df_idx}, 批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}): {llm_data_str[:200]}... Error: {e}")
                        parsed_llm_data_map[original_df_idx] = {
                            "description": {"score": 0, "advices": f"结果解析失败: {e}"}}

        # 2. 处理结果并填充DataFrame
        for idx_df in range(len(raw_df)):
            advice = ""
            llm_data = parsed_llm_data_map.get(idx_df)
            if llm_data:
                if llm_data.get("description"):
                    ad = llm_data["description"]
                    if ad.get('advices'):
                        if ad.get('score'):
                            if ad['score'] < 7:
                                advice = ad['advices']
                        elif not ad.get('score'):
                            advice = ad['advices']
            mark_list.append(advice)

        raw_df['功能描述备注'] = mark_list
    except Exception as e:
        logger.error(f"judge_eif 执行过程中发生错误: {e}", exc_info=True)
        raw_df['功能描述备注'] = [""] * len(raw_df)
    finally:
        return raw_df
    

async def get_check_content(username, project_name, start_pos):
    """
    获取检查表三附1的内容。
    Args:
        username (str): 用户名
        project_name (str): 项目名称
        start_pos (tuple): 起始行和列位置
    Returns:
        final_df (DataFrame): 处理后的DataFrame
        mark_list (List): 包含坐标信息的备注列表
        merged_list (List): 包含坐标信息的合并单元格列表
    """
    # 初始化
    final_df = pd.DataFrame()
    raw_df = pd.DataFrame()
    app_raw = pd.DataFrame()
    inte_raw = pd.DataFrame()
    mark_list = []
    merged_list = []
    try:
        # 优先读表格中的应用功能
        app_raw = read_app_func(username, project_name,
                               sheet_name="应用功能", start_pos=(3, 1))
        if app_raw is None or app_raw.empty:
            app_raw = read_app_func(username, project_name,
                                   sheet_name="表三附1（开发、集成工作量测算） ", start_pos=(13, 1))
        if app_raw is None or app_raw.empty:
            app_raw = read_app_func(username, project_name,
                                   sheet_name="表三附1（开发、集成工作量测算）", start_pos=(13, 1))
        # 如果表格中没有数据，则尝试从Word文档中读取应用功能
        if app_raw is None or app_raw.empty:
            app_raw = read_word_app(username, project_name)
        else:
            app_raw = read_excel_app(app_raw)

        inte_raw = read_app_func(username, project_name,
                                    sheet_name="应用交互", start_pos=(1, 1))
        inte_raw = read_excel_inte(inte_raw, project_name)
        # 如果表格中没有数据，则尝试从Word文档中读取集成功能
        if inte_raw is None or inte_raw.empty:
            inte_raw = read_word_inte(username, project_name)
        # 合并应用功能和集成功能数据
        raw_df = pd.concat([app_raw, inte_raw], ignore_index=True)
        # 遍历每一行，检查功能描述列是否为空或者“/，//，\，\\，-， ，"若是则删除该行
        for i in range(len(raw_df)):
            if not raw_df.iloc[i]['功能描述']:
                raw_df.drop(i, inplace=True)
            else:
                desc = raw_df.iloc[i]['功能描述'].strip()
                if desc in ["", "//", "/", "\\", "\\\\", " "]:
                    raw_df.drop(i, inplace=True)
        # 重置索引
        raw_df.reset_index(drop=True, inplace=True)
        # 生成功能描述
        raw_df = await mark_desc(raw_df)

        # 再分开应用功能和系统集成功能用以计算融合坐标
        app_df = raw_df.iloc[:len(app_raw),:]
        inte_df = raw_df.iloc[len(app_raw):,:]
        # 按照应用组、一级应用模块、二级应用模块、功能项的顺序依次进行排序，并重置序号列和索引
        app_df = app_df.sort_values(by=['应用组', '一级应用模块', '二级应用模块', '功能项'])
        app_df['序号'] = range(1, len(app_df) + 1)
        app_df.reset_index(drop=True, inplace=True)
        # 计算融合表格坐标，应用组、一级应用模块、二级应用模块、功能项相同的项进行合并
        for col in ['应用组', '一级应用模块', '二级应用模块', '功能项']:
            col_index = app_df.columns.get_loc(col) + start_pos[1]
            group_index = get_start_end(app_df, col)
            group_dict = {}
            for group in group_index:
                if group['name'] != '':
                    group_dict[group['name']] = (group['start'], group['end'])
            for _, value in group_dict.items():
                if value[1] - value[0] >= 1:
                    merged_list.append(
                        (value[0] + start_pos[0], col_index, value[1] + start_pos[0], col_index))
        inte_df = inte_df.sort_values(by=['应用组', '一级应用模块', '二级应用模块', '功能项'])
        inte_df['序号'] = range(len(app_df) + 1, len(raw_df) + 1)
        inte_df.reset_index(drop=True, inplace=True)
        for col in ['应用组', '一级应用模块', '二级应用模块', '功能项']:
            col_index = inte_df.columns.get_loc(col) + start_pos[1]
            group_index = get_start_end(inte_df, col)
            group_dict = {}
            for group in group_index:
                if group['name'] != '':
                    group_dict[group['name']] = (group['start'], group['end'])
            for _, value in group_dict.items():
                if value[1] - value[0] >= 1:
                    merged_list.append(
                        (value[0] + start_pos[0] + len(app_df), col_index, value[1] + start_pos[0] + len(app_df), col_index))
        # 合并应用功能和系统集成功能数据
        final_df = pd.concat([app_df, inte_df], ignore_index=True)

        # 根据功能描述备注填充mark_list
        for i in range(len(final_df)):
            mark = final_df.iloc[i]['功能描述备注']
            if mark:
                # 获取当前行的序号
                row_index = i + start_pos[0]
                # 将序号和备注信息添加到mark_list中
                mark_list.append((row_index, mark))
        # 删除不需要的列
        final_df.drop(columns=['功能描述备注'], inplace=True, errors='ignore')
        return final_df, mark_list, merged_list
    except Exception as e:
        logger.error(f"获取检查内容失败！{e}")
        return final_df, mark_list, merged_list


def save_json_file(content_list, mark_list, merged_list, request):
    """
    保存数据到JSON文件。
    Args:
        content_list (List): excel内容的二维列表
        mark_list (List): 包含坐标信息的备注列表
        merged_list (List): 包含坐标信息的合并单元格列表
        request (ExcelRequest): 请求对象，包含用户信息和项目名称
    """
    # 将响应数据转化为json文件写入
    response_data = {
        "status_code": 200,
        "message": "检查表三附1成功！",
        "data": {
            "content_list": content_list,
            "mark_list": mark_list,
            "merged_list": merged_list
        }
    }
    # 将响应数据保存为JSON文件，便于后续访问和处理
    try:
        # 从配置中获取根目录
        ROOT_DIRECTORY = settings.PROJECT_ROOT

        # 构建保存路径
        media_dir = os.path.join(
            ROOT_DIRECTORY, "static", request.username, request.project_name, "media")
        json_path = os.path.join(media_dir, "check_table.json")

        # 确保目录存在
        os.makedirs(media_dir, exist_ok=True)

        # 写入JSON文件
        with open(json_path, "w", encoding="utf-8") as f:
            json.dump(response_data, f, ensure_ascii=False, indent=4)

        logger.info(f"成功保存检查表格数据到: {json_path}")
    except Exception as e:
        # 记录错误但不中断流程，因为即使文件保存失败，API仍可返回数据
        logger.error(f"保存检查表格JSON文件失败: {str(e)}")


@router.post("/check_table", summary="检查Excel")
@api_response
async def check_table(request: ExcelRequest):
    """
    检查表三附1。
    Args:
        request (ProjectInfoRequest): 包含用户输入的项目名称
            - user_name (str): 用户名
            - project_name (str): 格式为"项目类（具体项目名称）"
            - start_row (int): 起始行号
            - start_col (int): 起始列号
    Returns:
        APIResponse: 包含处理结果的响应对象
            - status_code: 200 校验成功
            - message: 处理结果说明
            - data: 
                - content_list (List): excel内容的二维列表
                - mark_list (List): 包含坐标信息的备注列表
                - merged_list (List): 包含坐标信息的合并单元格列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    # 获取用户名和项目名称然后查询对应厂家资料，若不存在则返回错误信息提示其上传厂家资料
    try:
        start_pos = (request.start_row, request.start_col)
        final_df, mark_list, merged_list = await get_check_content(
            request.username, request.project_name, start_pos)

        if final_df is None or final_df.empty:
            raise HTTPException(status_code=404, detail="检查表三附1失败！未读取到相关资料！")
        
        # 转化为二维列表输出结果
        content_list = final_df.values.tolist()

        # 添加调试日志检查数据类型
        def check_numpy_types(obj, path=""):
            if isinstance(obj, np.number):
                return True
            elif isinstance(obj, (list, tuple)):
                found = False
                for i, item in enumerate(obj):
                    if check_numpy_types(item, f"{path}[{i}]"):
                        found = True
                return found
            elif isinstance(obj, dict):
                found = False
                for k, v in obj.items():
                    if check_numpy_types(v, f"{path}.{k}"):
                        found = True
                return found
            return False

        # 检查各个列表中的数据类型
        check_numpy_types(content_list, "content_list")
        check_numpy_types(mark_list, "mark_list")
        check_numpy_types(merged_list, "merged_list")
        # 转换NumPy类型为Python原生类型
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, list):
                return [convert_numpy_types(i) for i in obj]
            elif isinstance(obj, tuple):
                return tuple(convert_numpy_types(i) for i in obj)
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            return obj
        content_list = convert_numpy_types(content_list)
        mark_list = convert_numpy_types(mark_list)
        merged_list = convert_numpy_types(merged_list)

        # 保存数据到JSON文件
        save_json_file(content_list, mark_list, merged_list, request)

        # 返回API响应
        return APIResponse(
            status_code=200,
            message="检查表三附一成功",
            data={
                "content_list": content_list,
                "mark_list": mark_list,
                "merged_list": merged_list
            }
        )

    except Exception as e:
        logger.error(f"检查表三附一失败！{e}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误！{e}")
