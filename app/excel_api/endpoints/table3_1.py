# --coding:utf-8--
# 本文件用于存放以下接口：
# - 生成表三附1 /table3_1
import os
import json
import numpy as np  # 添加numpy导入
from app.core.logging_config import get_logger
import pandas as pd
from app.utils.llm import chat_with_llm, extract_json, async_chat_with_llm, distribute_queries_to_apis
from fastapi import APIRouter, HTTPException
from app.core.config import settings
from app.docs_api.schemas import ExcelRequest
from app.docs_api.endpoints.cover import parse_project_name
from app.utils.doc_processor import docx_search
from app.utils.response import APIResponse, api_response
from app.utils.tugraph import validate_node, search_func_logic
from app.templates.prompt_templates import identify_five_points_prompt_0311, deduplicate_prompt, identify_EIF
from app.utils.excel_processor import read_app_func, get_start_end, replace_standalone, insert_start_pos
import warnings
warnings.filterwarnings("ignore", category=UserWarning,
                        module="openpyxl.reader.drawings")
pd.set_option('future.no_silent_downcasting', True)
# 从配置中获取上传目录
PROJECT_ROOT = settings.PROJECT_ROOT
UPLOAD_DIRECTORY = settings.UPLOAD_DIRECTORY
LLM_PROCESSING_BATCH_SIZE = 64  # 批量处理的大小

router = APIRouter()
logger = get_logger(__name__)


def validate_app_func(start_row, start_col, func_df, col_list):
    """
    验证应用功能是否存在。
    Args:
        start_row (int): 起始行号
        start_col (int): 起始列号
        func_df (pandas.DataFrame): 应用功能项数据
        col_list (List): 应用功能项列名列表
    Returns:
        mark_list (List): 包含坐标信息的备注列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 生成备注列表
        mark_list = []
        for i in range(len(func_df)):
            for j in range(1, 6):
                # 查询图谱中是否存在该实体，若不存在则增加备注并跳出当前行
                if j == 2 or j == 3:
                    flag = validate_node(func_df.iloc[i, j], '应用模块')
                else:
                    flag = validate_node(func_df.iloc[i, j], col_list[j])
                if flag == False:
                    mark_list.append(
                        {"row": i + start_row, "col": j + start_col, "content": "图谱中未找到该应用或功能！"})
                    break

        return mark_list

    except Exception as e:
        logger.error(f"验证功能项失败！{str(e)}")
        raise HTTPException(status_code=500, detail=f"验证功能项失败！{str(e)}")


def delete_contents(content):
    """
    删除功能描述内容中输入信息、输出信息、查询内容所在的行。
    Args:
        content (str): 功能描述内容
    Returns:
        content (str): 删除输入信息、输出信息、查询内容后的内容
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 以换行符对内容进行切分
        lines = content.split('\n')
        content_list = []
        # 遍历每一行，若该行包含输入信息、输出信息、查询内容，则跳过该行
        for line in lines:
            if '输入信息' in line or '输出信息' in line or '查询内容' in line:
                continue
            content_list.append(line)
        # 将处理后的内容重新拼接
        content = '\n'.join(content_list).strip('\n').strip()
        return content
    except Exception as e:
        logger.error(f"删除功能描述内容失败！{str(e)}")
        logger.error(content)
        return content


def validate_logic_entity(func_df):
    """
    验证并补充逻辑实体（内部逻辑文件）。
    Args:
        func_df (pandas.DataFrame): 应用功能数据
    Returns:
        func_df (pandas.DataFrame): 补充逻辑实体后的应用功能数据
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 对每一行功能项查询其关联逻辑实体，若存在则补充
        for i in range(len(func_df)):
            # 获取功能项名称和功能子项名称及其相关逻辑实体
            func_name = func_df.iloc[i, 4]
            if func_name is None:
                func_name = ''
            sub_func_name = func_df.iloc[i, 5]
            if sub_func_name is None:
                sub_func_name = ''
            func_list = []
            logic_entity = ""
            if func_name not in ['/', '']:
                if validate_node(func_name, "功能项"):
                    func_list.append(func_name)
            if sub_func_name not in ['/', '']:
                if validate_node(sub_func_name, "功能子项"):
                    func_list.append(sub_func_name)
            if func_list:
                logic_entity_list = search_func_logic(func_list)
                if logic_entity_list:
                    logic_entity = "，".join(logic_entity_list)
            # 获取厂家资料中的逻辑实体
            if func_df.iloc[i, 7] not in ['/', '']:
                if func_df.iloc[i, 7] is None:
                    func_df.iloc[i, 7] = ''
                logic_entity = func_df.iloc[i, 7] + logic_entity
            func_df.iloc[i, 7] = logic_entity

    except Exception as e:
        logger.error(f"补充逻辑实体失败！{str(e)}")
    finally:
        return func_df


def validate_shape(app_df: pd.DataFrame):
    """
    检查是否列名是否为模板列名，若不是则新增列并插入空值
    Args:
        app_df (pandas.DataFrame): 应用功能数据
    Returns:
        app_df (pandas.DataFrame): 应用功能数据
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 定义模板列名
        template_columns = ['内部逻辑文件', '内部逻辑文件修改数', '内部逻辑文件调节数', '内部逻辑文件新增数',
                            '外部接口文件', '外部接口文件修改数', '外部接口文件调节数', '外部接口文件新增数',
                            '输入操作项', '输入操作项修改数', '输入操作项调节数', '输入操作项新增数',
                            '输出操作项', '输出操作项修改数', '输出操作项调节数', '输出操作项新增数',
                            '查询操作项', '查询操作项修改数', '查询操作项调节数', '查询操作项新增数']
        # 检查是否列名是否为模板列名，若不是则新增列并插入空值
        for col in template_columns:
            if col not in app_df.columns:
                app_df[col] = ''
        return app_df
    except Exception as e:
        logger.error(f"检查列名是否为模板列名失败！{str(e)}")
        return app_df


async def process_row(row, identify_prompt_template):
    func_desc = row['功能描述']
    row_idx = row.name if hasattr(row, 'name') else "未知行号"
    if func_desc != "" and func_desc != "/":
        user_query = identify_prompt_template + func_desc
        try:
            # 使用异步版本直接处理
            logger.info(f"处理行 {row_idx} 的功能描述开始")
            llm_data_str = await async_chat_with_llm(user_query)
            logger.info(f"处理行 {row_idx} 的功能描述完成")

        except Exception as e:
            logger.error(f"行 {row_idx} 调用 async_chat_with_llm 失败！{e}")
            llm_data_str = json.dumps({"description": {
                "score": 0,
                "advices": "调用大模型失败：" + str(e)
            }})  # Ensure it's a JSON string for extract_json
        try:
            # extract_json expects a string, which llm_data_str should be
            llm_data = extract_json(llm_data_str)
        except Exception as e:
            logger.error(f"行 {row_idx} 提示词：{user_query}")
            # Log the string that failed to parse
            logger.error(f"行 {row_idx} LLM返回：{llm_data_str}")
            logger.error(f"行 {row_idx} 解析大模型返回结果失败！{e}")
            if not llm_data_str:
                advice = "大模型返回结果为空！"
            else:
                # Show part of the problematic string
                advice = f"大模型返回结果无法解析：{llm_data_str[:500]}"
            llm_data = [{"description": {  # extract_json returns a list
                "score": 0,
                "advices": advice
            }}]
    else:
        logger.info(f"行 {row_idx} 功能描述为空，跳过处理")
        llm_data = [{}]  # extract_json expects to return a list of dicts
    return llm_data  # extract_json returns a list, so we return it directly


async def judge_change_add(func_df):
    """
    使用大模型判断更改新增数。
    Args:
        func_df (pandas.DataFrame): 应用功能数据
    Returns:
        func_df (pandas.DataFrame): 补充更改新增数后的应用功能数据
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 初始化列表...
        ilf_name, ilf_changed, ilf_regulate, ilf_new = [], [], [], []
        eif_name, eif_changed, eif_regulate, eif_new = [], [], [], []
        ei_name, ei_changed, ei_regulate, ei_new = [], [], [], []
        eo_name, eo_changed, eo_regulate, eo_new = [], [], [], []
        eq_name, eq_changed, eq_regulate, eq_new = [], [], [], []
        mark_list = []  # For description advice

        # 1. 准备所有查询
        # Stores tuples of (original_df_idx, query_string)
        all_queries_with_indices = []

        for idx, row in func_df.iterrows():
            func_desc = row['功能描述']
            if func_desc and func_desc != "/":
                all_queries_with_indices.append(
                    (idx, identify_five_points_prompt_0311 + func_desc)
                )
            else:
                # For rows with no func_desc, we'll add default empty values later
                pass

        # Create a dictionary to store llm_data for valid rows, keyed by original index
        parsed_llm_data_map = {}

        if all_queries_with_indices:
            logger.info(f"准备分批处理 {len(all_queries_with_indices)} 条有效的功能描述...")

            for i in range(0, len(all_queries_with_indices), LLM_PROCESSING_BATCH_SIZE):
                batch_queries_with_indices = all_queries_with_indices[i:i +
                                                                      LLM_PROCESSING_BATCH_SIZE]
                current_batch_queries = [item[1]
                                         for item in batch_queries_with_indices]
                current_batch_indices = [item[0]
                                         for item in batch_queries_with_indices]

                logger.info(
                    f"正在处理批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}，包含 {len(current_batch_queries)} 条查询。")

                batch_llm_results_str = []
                try:  # Ensuring this try is correctly formatted
                    batch_llm_results_str = await distribute_queries_to_apis(current_batch_queries)
                    logger.info(
                        f"批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}: 成功从LLM批量获取 {len(batch_llm_results_str)} 条结果。")
                except Exception as e:  # Ensuring this except is aligned with the try
                    logger.error(
                        f"批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}: 调用 distribute_queries_to_apis 批量处理失败: {e}. 对该批次进行逐行回退处理。")
                    # Fallback to row-by-row processing for this batch if batch fails
                    temp_results_for_fallback = []
                    for query_idx_in_batch, query_for_fallback in enumerate(current_batch_queries):
                        original_df_idx_for_fallback = current_batch_indices[query_idx_in_batch]
                        try:
                            single_res = await async_chat_with_llm(query_for_fallback)
                            temp_results_for_fallback.append(single_res)
                        except Exception as single_e:
                            logger.error(
                                f"Fallback (批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}, 原始索引 {original_df_idx_for_fallback}): 单行处理失败. Error: {single_e}")
                            temp_results_for_fallback.append(json.dumps(
                                {"description": {"score": 0, "advices": f"调用大模型失败: {single_e}"}}))
                    batch_llm_results_str = temp_results_for_fallback

                # Process results for the current batch (either from try or except block above)
                for batch_item_idx, llm_data_str in enumerate(batch_llm_results_str):
                    original_df_idx = current_batch_indices[batch_item_idx]
                    try:
                        llm_answer = extract_json(llm_data_str)
                        if llm_answer == []:
                            logger.error(
                                f"解析来自LLM的结果失败 (原始索引 {original_df_idx}, 批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}): {llm_data_str[:200]}... 结果为空。")
                            parsed_llm_data_map[original_df_idx] = {
                                "description": {"score": 0, "advices": f"结果解析失败: {e}，大模型回答：{llm_data_str}"}}
                        else:
                            parsed_llm_data_map[original_df_idx] = llm_answer[0]
                    except Exception as e:
                        logger.error(
                            f"解析来自LLM的结果失败 (原始索引 {original_df_idx}, 批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}): {llm_data_str[:200]}... Error: {e}")
                        parsed_llm_data_map[original_df_idx] = {
                            "description": {"score": 0, "advices": f"结果解析失败: {e}"}}

        # 2. 处理结果并填充DataFrame
        # This block should be at the same indent level as 'if all_queries_with_indices:'
        # Renamed loop variable to avoid potential confusion
        for idx_df in range(len(func_df)):
            # Get data if this row was processed
            llm_data = parsed_llm_data_map.get(idx_df)

            if not llm_data:  # This row had no func_desc or parsing failed to an empty dict
                mark_list.append("")
                ilf_name.append("")
                ilf_changed.append(0)
                ilf_regulate.append(0.4)
                ilf_new.append(0)
                eif_name.append("")
                eif_changed.append(0)
                eif_regulate.append(0.5)
                eif_new.append(0)
                ei_name.append("")
                ei_changed.append(0)
                ei_regulate.append(0.4)
                ei_new.append(0)
                eo_name.append("")
                eo_changed.append(0)
                eo_regulate.append(0.4)
                eo_new.append(0)
                eq_name.append("")
                eq_changed.append(0)
                eq_regulate.append(0.4)
                eq_new.append(0)
                continue

            advice = ""
            if llm_data.get("description"):
                ad = llm_data["description"]
                if ad.get('advices'):
                    if ad.get('score'):
                        if int(ad['score']) < 7:
                            advice = ad['advices']
                    else:
                        advice = ad['advices']
            mark_list.append(advice)

            # Populate ilf_... lists
            ilf_list_vals = ["", 0, 0.4, 0]
            if llm_data.get("internal_logical_files"):
                ilf = llm_data["internal_logical_files"]
                if ilf.get('name') and isinstance(ilf['name'], list):
                    ilf_list_vals[0] = '，'.join(
                        [n for n in ilf['name'] if not str(n).startswith("file")])
                    ilf_list_vals[3] = len(ilf['name'])
            ilf_name.append(ilf_list_vals[0])
            ilf_changed.append(ilf_list_vals[1])
            ilf_regulate.append(ilf_list_vals[2])
            ilf_new.append(ilf_list_vals[3])

            # Populate eif_... lists
            eif_list_vals = ["", 0, 0.5, 0]
            if llm_data.get("external_interface_files"):
                eif = llm_data["external_interface_files"]
                if eif.get('name') and isinstance(eif['name'], list):
                    eif_list_vals[0] = '，'.join(
                        [n for n in eif['name'] if not str(n).startswith("file")])
                if eif.get('changed'):
                    eif_list_vals[1] = eif['changed']
                if eif.get('new'):
                    eif_list_vals[3] = eif['new']
            eif_name.append(eif_list_vals[0])
            eif_changed.append(eif_list_vals[1])
            eif_regulate.append(eif_list_vals[2])
            eif_new.append(eif_list_vals[3])

            # Populate ei_... lists
            ei_list_vals = ["", 0, 0.4, 0]
            if llm_data.get("external_inputs"):
                ei = llm_data["external_inputs"]
                if ei.get('name') and isinstance(ei['name'], list):
                    ei_list_vals[0] = '，'.join(
                        [n for n in ei['name'] if not str(n).startswith("input")])
                if ei.get('changed'):
                    ei_list_vals[1] = ei['changed']
                if ei.get('new'):
                    ei_list_vals[3] = ei['new']
            ei_name.append(ei_list_vals[0])
            ei_changed.append(ei_list_vals[1])
            ei_regulate.append(ei_list_vals[2])
            ei_new.append(ei_list_vals[3])

            # Populate eo_... lists
            eo_list_vals = ["", 0, 0.4, 0]
            if llm_data.get("external_outputs"):
                eo = llm_data["external_outputs"]
                if eo.get('name') and isinstance(eo['name'], list):
                    eo_list_vals[0] = '，'.join(
                        [n for n in eo['name'] if not str(n).startswith("output")])
                if eo.get('changed'):
                    eo_list_vals[1] = eo['changed']
                if eo.get('new'):
                    eo_list_vals[3] = eo['new']
            eo_name.append(eo_list_vals[0])
            eo_changed.append(eo_list_vals[1])
            eo_regulate.append(eo_list_vals[2])
            eo_new.append(eo_list_vals[3])

            # Populate eq_... lists
            eq_list_vals = ["", 0, 0.4, 0]
            if llm_data.get("external_inquiries"):
                eq = llm_data["external_inquiries"]
                if eq.get('name') and isinstance(eq['name'], list):
                    eq_list_vals[0] = '，'.join(
                        [n for n in eq['name'] if not str(n).startswith("inquiry")])
                if eq.get('changed'):
                    eq_list_vals[1] = eq['changed']
                if eq.get('new'):
                    eq_list_vals[3] = eq['new']
            eq_name.append(eq_list_vals[0])
            eq_changed.append(eq_list_vals[1])
            eq_regulate.append(eq_list_vals[2])
            eq_new.append(eq_list_vals[3])

        # Assign lists to DataFrame columns
        func_df['功能描述备注'] = mark_list
        func_df['内部逻辑文件'] = ilf_name
        func_df['内部逻辑文件修改数'] = ilf_changed
        func_df['内部逻辑文件调节数'] = ilf_regulate
        func_df['内部逻辑文件新增数'] = ilf_new
        func_df['外部接口文件'] = eif_name
        func_df['外部接口文件修改数'] = eif_changed
        func_df['外部接口文件调节数'] = eif_regulate
        func_df['外部接口文件新增数'] = eif_new
        func_df['输入操作项'] = ei_name
        func_df['输入操作项修改数'] = ei_changed
        func_df['输入操作项调节数'] = ei_regulate
        func_df['输入操作项新增数'] = ei_new
        func_df['输出操作项'] = eo_name
        func_df['输出操作项修改数'] = eo_changed
        func_df['输出操作项调节数'] = eo_regulate
        func_df['输出操作项新增数'] = eo_new
        func_df['查询操作项'] = eq_name
        func_df['查询操作项修改数'] = eq_changed
        func_df['查询操作项调节数'] = eq_regulate
        func_df['查询操作项新增数'] = eq_new

    except Exception as e:
        logger.error(f"judge_change_add 执行过程中发生错误: {e}", exc_info=True)
        func_df = validate_shape(func_df)
        func_df['功能描述备注'] = [""] * len(func_df)
    finally:
        return func_df


async def judge_eif(inte_df):
    """
    使用大模型判断外部接口文件。
    Args:
        inte_df (pandas.DataFrame): 应用功能数据
    Returns:
        inte_df (pandas.DataFrame): 补充外部接口文件后的应用功能数据
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 初始化列表...
        eif_name, eif_changed, eif_regulate, eif_new = [], [], [], []
        mark_list = []  # For description advice

        # 1. 准备所有查询
        # Stores tuples of (original_df_idx, query_string)
        all_queries_with_indices = []
        for idx, row in inte_df.iterrows():
            func_desc = row['功能描述']
            if func_desc and func_desc != "/":
                all_queries_with_indices.append(
                    (idx, identify_EIF + func_desc))
            else:
                pass

        parsed_llm_data_map = {}

        if all_queries_with_indices:
            logger.info(
                f"准备分批处理 {len(all_queries_with_indices)} 条有效的功能描述 (judge_eif)...")

            for i in range(0, len(all_queries_with_indices), LLM_PROCESSING_BATCH_SIZE):
                batch_queries_with_indices = all_queries_with_indices[i:i +
                                                                      LLM_PROCESSING_BATCH_SIZE]
                current_batch_queries = [item[1]
                                         for item in batch_queries_with_indices]
                current_batch_indices = [item[0]
                                         for item in batch_queries_with_indices]

                logger.info(
                    f"judge_eif: 正在处理批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}，包含 {len(current_batch_queries)} 条查询。")

                batch_llm_results_str = []
                try:  # Ensuring this try is correctly formatted
                    batch_llm_results_str = await distribute_queries_to_apis(current_batch_queries)
                    logger.info(
                        f"judge_eif (批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}): 成功从LLM批量获取 {len(batch_llm_results_str)} 条结果。")
                except Exception as e:  # Ensuring this except is aligned with the try
                    logger.error(
                        f"judge_eif (批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}): 调用 distribute_queries_to_apis 批量处理失败: {e}. 对该批次进行逐行回退处理。")
                    temp_results_for_fallback = []
                    for query_idx_in_batch, query_for_fallback in enumerate(current_batch_queries):
                        original_df_idx_for_fallback = current_batch_indices[query_idx_in_batch]
                        try:
                            single_res = await async_chat_with_llm(query_for_fallback)
                            temp_results_for_fallback.append(single_res)
                        except Exception as single_e:
                            logger.error(
                                f"Fallback (judge_eif, 批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}, 原始索引 {original_df_idx_for_fallback}): 单行处理失败. Error: {single_e}")
                            temp_results_for_fallback.append(json.dumps(
                                {"description": {"score": 0, "advices": f"调用大模型失败: {single_e}"}}))
                    batch_llm_results_str = temp_results_for_fallback

                # Process results for the current batch (either from try or except block above)
                for batch_item_idx, llm_data_str in enumerate(batch_llm_results_str):
                    original_df_idx = current_batch_indices[batch_item_idx]
                    try:
                        llm_answer = extract_json(llm_data_str)
                        if llm_answer == []:
                            logger.error(
                                f"解析来自LLM的结果失败 (原始索引 {original_df_idx}, 批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}): {llm_data_str[:200]}... 结果为空。")
                            parsed_llm_data_map[original_df_idx] = {
                                "description": {"score": 0, "advices": f"结果解析失败: {e}，大模型回答：{llm_data_str}"}}
                        else:
                            parsed_llm_data_map[original_df_idx] = llm_answer[0]
                    except Exception as e:
                        logger.error(
                            f"解析来自LLM的结果失败 (judge_eif, 原始索引 {original_df_idx}, 批次 {i // LLM_PROCESSING_BATCH_SIZE + 1}): {llm_data_str[:200]}... Error: {e}")
                        parsed_llm_data_map[original_df_idx] = {
                            "description": {"score": 0, "advices": f"结果解析失败: {e}"}}

        # 2. 处理结果并填充DataFrame
        # This block should be at the same indent level as 'if all_queries_with_indices:'
        for idx_df in range(len(inte_df)):  # Renamed loop variable
            # Get data if this row was processed
            llm_data = parsed_llm_data_map.get(idx_df)
            if not llm_data:  # This row had no func_desc or parsing failed to an empty dict
                mark_list.append("")
                eif_name.append("")
                eif_changed.append(0)
                eif_regulate.append(0.5)
                eif_new.append(0)
                continue
            advice = ""
            if llm_data.get("description"):
                ad = llm_data["description"]
                if ad.get('advices'):
                    if ad.get('score'):
                        if ad['score'] < 7:
                            advice = ad['advices']
                    elif not ad.get('score'):
                        advice = ad['advices']
            mark_list.append(advice)

            # Populate eif_... lists
            eif_list_vals = ["", 0, 0.5, 0]
            if llm_data.get("external_interface_files"):
                eif = llm_data["external_interface_files"]
                if eif.get('name') and isinstance(eif['name'], list):
                    eif_list_vals[0] = '，'.join(
                        [n for n in eif['name'] if not str(n).startswith("file")])
                if eif.get('changed'):
                    eif_list_vals[1] = eif['changed']
                if eif.get('new'):
                    eif_list_vals[3] = eif['new']
            eif_name.append(eif_list_vals[0])
            eif_changed.append(eif_list_vals[1])
            eif_regulate.append(eif_list_vals[2])
            eif_new.append(eif_list_vals[3])

        # Assign lists to DataFrame columns
        list_len = len(mark_list)
        inte_df['功能描述备注'] = mark_list
        inte_df['内部逻辑文件'] = [""] * list_len
        inte_df['内部逻辑文件修改数'] = [0] * list_len
        inte_df['内部逻辑文件调节数'] = [0.4] * list_len
        inte_df['内部逻辑文件新增数'] = [0] * list_len
        inte_df['外部接口文件'] = eif_name
        inte_df['外部接口文件修改数'] = eif_changed
        inte_df['外部接口文件调节数'] = eif_regulate
        inte_df['外部接口文件新增数'] = eif_new
        inte_df['输入操作项'] = [""] * list_len
        inte_df['输入操作项修改数'] = [0] * list_len
        inte_df['输入操作项调节数'] = [0.4] * list_len
        inte_df['输入操作项新增数'] = [0] * list_len
        inte_df['输出操作项'] = [""] * list_len
        inte_df['输出操作项修改数'] = [0] * list_len
        inte_df['输出操作项调节数'] = [0.4] * list_len
        inte_df['输出操作项新增数'] = [0] * list_len
        inte_df['查询操作项'] = [""] * list_len
        inte_df['查询操作项修改数'] = [0] * list_len
        inte_df['查询操作项调节数'] = [0.4] * list_len
        inte_df['查询操作项新增数'] = [0] * list_len
    except Exception as e:
        logger.error(f"judge_eif 执行过程中发生错误: {e}", exc_info=True)
        inte_df = validate_shape(inte_df)
        inte_df['功能描述备注'] = [""] * len(inte_df)
    finally:
        return inte_df


def convert_paragraphs(paragraphs):
    """
    将段落列表转换为二维列表，处理标题和正文段落。
    Args:
        paragraphs (List[Dict]): 段落列表，每个段落是一个字典，包含 'style' 和 'content' 键。
    Returns:
        List[List[str]]: 转换后的二维列表，每行包含正文内容和对应的标题。
    Raises:
        ValueError: 如果段落列表为空或格式不正确。
    """
    try:
        result = []
        title_stack = []         # 当前各层级标题
        buffered_content = ''    # 正文缓存
        max_level_seen = 0       # 全局最大标题层级

        for para in paragraphs:
            level = int(para['style'])-3
            content = para['content']

            if level > 0:  # 是标题
                current_max_level = len(title_stack)

                if buffered_content:
                    # 判断新标题是否比当前标题栈的层级高
                    if level > current_max_level:
                        # 舍弃缓存内容，不清空标题栈，仅更新标题
                        buffered_content = ''
                    else:
                        # 将缓存正文写入结果
                        row = [buffered_content]
                        for i in range(max_level_seen):
                            if i < len(title_stack):
                                row.append(title_stack[i])
                            else:
                                row.append('')
                        result.append(row)
                        buffered_content = ''

                # 更新标题栈
                if level <= len(title_stack):
                    title_stack[level - 1] = content
                    title_stack = title_stack[:level]
                else:
                    while len(title_stack) < level - 1:
                        title_stack.append('')
                    title_stack.append(content)

                # 更新全局最大标题层级
                if level > max_level_seen:
                    max_level_seen = level

            elif level == -3:  # 是正文
                if buffered_content:
                    buffered_content += '\n' + content
                else:
                    buffered_content = content

        # 处理最后可能存在的缓存正文
        if buffered_content:
            row = [buffered_content]
            for i in range(max_level_seen):
                if i < len(title_stack):
                    row.append(title_stack[i])
                else:
                    row.append('')
            result.append(row)

        return result

    except Exception as e:
        logger.error(f"转换段落列表失败: {e}")
        return []


def process_2d_list(group_name, data):
    """
    处理二维列表，确保每行有五列：内容、一级标题、二级标题、三级标题、四级标题。
    Args:
        group_name (str): 应用组名称
        data (List[List[str]]): 二维列表，每行包含内容和标题。
    Returns:
        List[List[str]]: 处理后的二维列表，每行包含内容和四个标题。
    Raises:
        ValueError: 如果输入数据格式不正确或处理失败。
    """

    if not data:
        return []
    result = []

    try:
        for row in data:
            content = row[0]
            titles = row[1:]

            # 找到最后一个非空字符串的索引
            last_title_index = -1
            for i, title in enumerate(titles):
                if title.strip():
                    last_title_index = i

            # 判断是否有四级或以上标题
            has_deep_titles = last_title_index >= 3  # 索引3对应四级标题

            if has_deep_titles:
                # 保留一级、二级 + 截取最后两级标题作为三、四级
                new_title1 = titles[0] if len(titles) > 0 else ''
                new_title2 = titles[1] if len(titles) > 1 else ''
                new_title3 = titles[last_title_index -
                                    1] if last_title_index - 1 >= 0 else ''
                new_title4 = titles[last_title_index] if last_title_index >= 0 else ''
            else:
                # 少于四级标题的情况：使用已有标题，不足的复制最后一级非空标题
                title_values = [t for t in titles if t.strip()]
                last_title = title_values[-1] if title_values else ''

                new_title1 = title_values[0] if len(title_values) > 0 else ''
                new_title2 = title_values[1] if len(
                    title_values) > 1 else last_title
                new_title3 = title_values[2] if len(
                    title_values) > 2 else last_title
                new_title4 = last_title
            if new_title1:
                result.append(["", group_name, new_title1,
                              new_title2, new_title3, new_title4, content])

        return result

    except Exception as e:
        logger.error(f"处理二维列表失败: {e}")
        return []


def read_word_func(username, project_name) -> pd.DataFrame:
    """
    读取word厂家资料。
    Args:
        username (str): 用户名
        project_name (str): 项目名称
    Returns:
        func_df (pandas.DataFrame): 应用功能数据
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 读取word厂家资料
        # 获取原始内容
        source_path = os.path.join(UPLOAD_DIRECTORY, username, project_name)
        origin_results = docx_search(username, project_name, '应用功能')
        if not origin_results:
            raise HTTPException(status_code=404, detail="未找到word中应用功能相关资料！")
        # 初始化列名
        columns = ['序号', '应用组', '一级应用模块', '二级应用模块', '功能项', '功能子项', '功能描述']
        # 用于保存所有行的数据
        rows = []
        # 用于保存上一行的部分数据
        last_row_data = {}

        # 读取config文件获取建设单位
        construction_unit = "南方电网"
        config_path = os.path.join(source_path, 'config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as file:
                config_data = json.load(file)
                if config_data.get('construction_unit'):
                    construction_unit = config_data['construction_unit']

        if construction_unit != "广东电网公司":

            index = 1  # 序号初始化
            i = 0  # content列表索引初始化
            content = []
            for i in range(len(origin_results)):
                if origin_results[i]['style'] == '0':
                    origin_results[i]['style'] = '10'
                if int(origin_results[i]['style']) > 0:
                    content.append(origin_results[i])
            i = 0
            while i < len(content):
                if content[i]['style'] == '4':
                    last_row_data['应用组'] = content[i]['content']
                elif content[i]['style'] == '5':
                    last_row_data['一级应用模块'] = content[i]['content']
                elif content[i]['style'] == '6':
                    last_row_data['二级应用模块'] = content[i]['content']
                elif content[i]['style'] == '7':
                    last_row_data['功能项'] = content[i]['content']
                    i += 1
                    while i < len(content) and int(content[i]['style']) > 7:
                        if content[i]['style'] == '8':
                            # 若已经存在功能描述则先保存
                            if last_row_data.get('功能描述'):
                                rows.append(
                                    {**{'序号': index}, **last_row_data.copy()})
                                index += 1
                                last_row_data['功能描述'] = ""
                            last_row_data['功能子项'] = content[i]['content']
                            i += 1
                            while i < len(content) and int(content[i]['style']) > 8:
                                if content[i]['style'] == '10' and last_row_data.get('功能描述'):
                                    last_row_data['功能描述'] += '\n' + \
                                        content[i]['content']
                                elif content[i]['style'] == '10' and not last_row_data.get('功能描述'):
                                    last_row_data['功能描述'] = content[i]['content']
                                i += 1
                            if last_row_data.get('功能描述'):
                                rows.append(
                                    {**{'序号': index}, **last_row_data.copy()})
                                index += 1
                                last_row_data['功能描述'] = ""
                            continue
                        elif content[i]['style'] == '10' and last_row_data.get('功能描述'):
                            last_row_data['功能描述'] += '\n' + \
                                content[i]['content']
                        elif content[i]['style'] == '10' and not last_row_data.get('功能描述'):
                            last_row_data['功能子项'] = '/'
                            last_row_data['功能描述'] = content[i]['content']
                        i += 1
                    if last_row_data.get('功能描述'):
                        rows.append({**{'序号': index}, **last_row_data.copy()})
                        index += 1
                        last_row_data['功能描述'] = ""
                    continue
                i += 1

        else:
            _, group_name = parse_project_name(project_name)
            rows = convert_paragraphs(origin_results)
            rows = process_2d_list(group_name, rows)
            if not rows:
                raise HTTPException(
                    status_code=404, detail="未找到word中应用功能相关资料！")

        # 创建DataFrame
        df = pd.DataFrame(rows, columns=columns)
        return df

    except Exception as e:
        logger.error(f"从Word中读取应用功能失败！{e}")
        return None


def dedup_lf_name(func_df, inte_df, col="内部逻辑文件"):
    """
    对内部逻辑文件名称进行去重。
    Args:
        func_df (pandas.DataFrame): 应用功能数据
        col (str): 列名
    Returns:
        func_df (pandas.DataFrame): 去重后的应用功能数据
    """
    try:
        app_df = func_df.copy(deep=True)
        int_df = inte_df.copy(deep=True)
        if col == "外部接口文件":
            file_list = list(set(int_df[col].tolist()))
            file_list = [file for file in file_list if file != ""]
        else:
            file_list = list(set(app_df[col].tolist()))
            file_list = [file for file in file_list if file != ""]
        user_query = deduplicate_prompt + str(file_list)
        dedup_dict = {}
        for i in range(3):
            # 打印提示词
            logger.info(f"第 {i+1}/3 次去重{col}名称！")
            # 询问大模型获取去重后的二维列表
            try:
                llm_answer = chat_with_llm(user_query)
            except Exception as e:
                logger.error(f"获取大模型返回结果失败！{e}")
                logger.error(f"提示词：{user_query}")
                llm_answer = "获取大模型返回结果失败！"
            try:
                llm_list = extract_json(llm_answer)
                if llm_list == []:
                    logger.error(
                        f"解析来自LLM的结果失败: {llm_answer[:200]}... 结果为空。")
                    continue
                else:
                    dedup_dict = llm_list[0]
                break
            except Exception as e:
                logger.error(f"解析大模型返回结果失败！{e}")
                logger.error(f"回答：{llm_answer}")
        if dedup_dict != {}:
            for key, value in dedup_dict.items():
                if key != value:
                    # 遍历每一行，若该行包含key，则将其替换为value
                    for index, row in app_df.iterrows():
                        files = row[col]
                        if key in files:
                            app_df.at[index, col] = files.replace(key, value)
                    logger.info(app_df)
                    for index, row in int_df.iterrows():
                        files = row[col]
                        if key in files:
                            int_df.at[index, col] = files.replace(key, value)
        else:
            logger.info(f"{col}名称中未发现重复！{file_list}")
        return app_df, int_df
    except Exception as e:
        logger.error(f"去重{col}名称失败！{e}")
        return func_df, inte_df


def handle_duplicate_values(df, col):
    """
    处理指定列的重复值。
    Args:
        df (pandas.DataFrame): 数据框
        col (str): 列名
    Returns:
        pandas.DataFrame: 处理后的数据框
    """
    try:
        # 初始化
        name_dict = {}
        for i in range(len(df)):
            if col == '外部接口文件':
                index = len(df) - i - 1
            else:
                index = i
            row = df.iloc[index]
            raw_name_list = row[col].split('，')
            try:
                adds = int(row[col+'新增数'])
            except:
                adds = len(raw_name_list)
            # 先对该行去重并调整adds的值
            name_list = []
            for name in raw_name_list:
                if name not in name_list:
                    name_list.append(name)
                else:
                    if adds > 0:
                        adds = adds - 1
            result = []
            for name in name_list:
                if name in name_dict:
                    name = name + "【第" + str(name_dict[name]) + "行已出现】"
                    if adds > 0:
                        adds = adds - 1
                else:
                    if name not in ['', '/']:
                        name_dict[name] = index + 1
                result.append(name)
            df.at[index, col] = '；'.join(result)
            df.at[index, col+'新增数'] = adds

    except Exception as e:
        logger.error(f"表三附一处理重复值失败！{e}")
    finally:
        return df


def formula_table3_1(app_df: pd.DataFrame, inte_df: pd.DataFrame, start_pos: tuple):
    """
    获取公式版表三附1。
    Args:
        app_df (pandas.DataFrame): 应用功能数据
        inte_df (pandas.DataFrame): 集成功能数据
        start_pos (tuple): 起始位置
    Returns:
        app_df (pandas.DataFrame): 公式版表三附1
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 深度复制应用数据
        formula_df = app_df.copy(deep=True)
        app_i = len(app_df)
        if inte_df is not None and not inte_df.empty:
            inte_i = len(inte_df)
            app_i = app_i - inte_i
        else:
            inte_i = app_i
        # 新增一列通用度因子
        formula_df['通用度因子'] = "=计算参数表!$D$75"

        # 逐行增加一列功能点数、功能点法预算工作量(人天)、开发(集成)工作量(人天)
        for index, _ in formula_df.iterrows():
            r = str(index + start_pos[0])
            if index < app_i:
                formula_df.at[index, '耗时率'] = "=计算参数表!$D$83"
                formula_df.at[index, '功能点数'] = '=((I'+r+'*J'+r+'+K'+r+')*计算参数表!$E$58+(M'+r+'*N'+r+'+O'+r+')*计算参数表!$E$59+(Q'+r+'*R'+r+'+S' + \
                    r+')*计算参数表!$E$60+(U'+r+'*V'+r+'+W'+r+')*计算参数表!$E$61+(Y'+r+'*Z' + \
                    r+'+AA'+r+')*计算参数表!$E$62)*AB'+r+'*计算参数表!$D$66*计算参数表!$D$70'
                formula_df.at[index,
                              '功能点法预算工作量(人天)'] = '=ROUND(AD'+r+'*AC'+r+'/8,2)'
                formula_df.at[index, '开发(集成)工作量(人天)'] = '=ROUND(AE' + \
                    r+'*(9/10)*计算参数表!$C$24,2)'
            else:
                formula_df.at[index, '耗时率'] = "=计算参数表!$D$84"
                formula_df.at[index, '功能点数'] = '=((I'+r+'*J'+r+'+K'+r+')*计算参数表!$E$58+(M'+r+'*N'+r+'+O'+r+')*计算参数表!$E$59+(Q'+r+'*R'+r+'+S' + \
                    r+')*计算参数表!$E$60+(U'+r+'*V'+r+'+W'+r+')*计算参数表!$E$61+(Y'+r+'*Z' + \
                    r+'+AA'+r+')*计算参数表!$E$62)*AB'+r+'*计算参数表!$D$66*计算参数表!$D$70'
                formula_df.at[index,
                              '功能点法预算工作量(人天)'] = '=ROUND(AD'+r+'*AC'+r+'/8,2)'
                formula_df.at[index, '开发(集成)工作量(人天)'] = '=ROUND(AE' + \
                    r+'*(9/10)*计算参数表!$C$25*计算参数表!$C$26,2)'

        # 新增一行总计
        end_index = str(len(formula_df) + start_pos[0] - 1)
        sum_row = {"序号": '总计', "应用组": "", "一级应用模块": "", "二级应用模块": "", "功能项": "", "功能子项": "", "功能描述": "", "逻辑实体": "", "内部更新数": "", "内部调节数": "", "内部新增数": "", "外部接口清单": "", "外部更新数": "", "外部调节数": "", "外部新增数": "", "输入操作项": "", "输入更新数": "",
                   "输入调解数": "", "输入新增数": "", "输出操作项": "", "输出更新数": "", "输出调解数": "", "输出新增数": "", "查询操作项": "", "查询更新数": "", "查询调解数": "", "查询新增数": "", "通用度因子": "", "耗时率": "", "功能点数": "=SUM(AD"+str(start_pos[0])+":AD"+end_index+")", "功能点法预算工作量(人天)": "=SUM(AE"+str(start_pos[0])+":AE"+end_index+")", "开发(集成)工作量(人天)": "=SUM(AF"+str(start_pos[0])+":AF"+end_index+")"}
        formula_df.loc[len(formula_df)] = sum_row
        if inte_df is not None and not inte_df.empty:
            dev_row = {"序号": '', "应用组": "", "一级应用模块": "", "二级应用模块": "", "功能项": "", "功能子项": "", "功能描述": "", "逻辑实体": "", "内部更新数": "", "内部调节数": "", "内部新增数": "", "外部接口清单": "", "外部更新数": "", "外部调节数": "", "外部新增数": "", "输入操作项": "", "输入更新数": "",
                   "输入调解数": "", "输入新增数": "", "输出操作项": "", "输出更新数": "", "输出调解数": "", "输出新增数": "", "查询操作项": "", "查询更新数": "", "查询调解数": "", "查询新增数": "", "通用度因子": "", "耗时率": "", "功能点数": "", "功能点法预算工作量(人天)": "开发费（万元）", "开发(集成)工作量(人天)": "=SUM(AF"+str(start_pos[0])+":AF"+str(inte_i + start_pos[0] - 1)+")*计算参数表!D89/10000"}
            inter_row = {"序号": '', "应用组": "", "一级应用模块": "", "二级应用模块": "", "功能项": "", "功能子项": "", "功能描述": "", "逻辑实体": "", "内部更新数": "", "内部调节数": "", "内部新增数": "", "外部接口清单": "", "外部更新数": "", "外部调节数": "", "外部新增数": "", "输入操作项": "", "输入更新数": "",
                         "输入调解数": "", "输入新增数": "", "输出操作项": "", "输出更新数": "", "输出调解数": "", "输出新增数": "", "查询操作项": "", "查询更新数": "", "查询调解数": "", "查询新增数": "", "通用度因子": "", "耗时率": "", "功能点数": "", "功能点法预算工作量(人天)": "集成费（万元）", "开发(集成)工作量(人天)": "=SUM(AF"+str(inte_i + start_pos[0])+":AF"+end_index+")*计算参数表!D89/10000"}
        else:
            dev_row = {"序号": '', "应用组": "", "一级应用模块": "", "二级应用模块": "", "功能项": "", "功能子项": "", "功能描述": "", "逻辑实体": "", "内部更新数": "", "内部调节数": "", "内部新增数": "", "外部接口清单": "", "外部更新数": "", "外部调节数": "", "外部新增数": "", "输入操作项": "", "输入更新数": "",
                   "输入调解数": "", "输入新增数": "", "输出操作项": "", "输出更新数": "", "输出调解数": "", "输出新增数": "", "查询操作项": "", "查询更新数": "", "查询调解数": "", "查询新增数": "", "通用度因子": "", "耗时率": "", "功能点数": "", "功能点法预算工作量(人天)": "开发费（万元）", "开发(集成)工作量(人天)": "=SUM(AF"+str(start_pos[0])+":AF"+end_index+")*计算参数表!D89/10000"}
            inter_row = {"序号": '', "应用组": "", "一级应用模块": "", "二级应用模块": "", "功能项": "", "功能子项": "", "功能描述": "", "逻辑实体": "", "内部更新数": "", "内部调节数": "", "内部新增数": "", "外部接口清单": "", "外部更新数": "", "外部调节数": "", "外部新增数": "", "输入操作项": "", "输入更新数": "",
                         "输入调解数": "", "输入新增数": "", "输出操作项": "", "输出更新数": "", "输出调解数": "", "输出新增数": "", "查询操作项": "", "查询更新数": "", "查询调解数": "", "查询新增数": "", "通用度因子": "", "耗时率": "", "功能点数": "", "功能点法预算工作量(人天)": "集成费（万元）", "开发(集成)工作量(人天)": "=0"}
        formula_df.loc[len(formula_df)] = dev_row
        formula_df.loc[len(formula_df)] = inter_row

        return formula_df

    except Exception as e:
        logger.error(f"获取公式版表三附1失败！{str(e)}")
        raise HTTPException(status_code=500, detail=f"获取公式版表三附1失败！{str(e)}")


def num_table3_1(app_df: pd.DataFrame, inte_df: pd.DataFrame, start_pos: tuple, username, project_name):
    """
    获取数值版表三附1并保存到json文件。
    Args:
        app_df (pandas.DataFrame): 应用功能数据
        start_pos (tuple): 起始位置
    Returns:
        num_df (pandas.DataFrame): 数值版表三附1
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 深度复制应用数据
        num_df = app_df.copy(deep=True)
        # 新增一列通用度因子
        num_df['通用度因子'] = 0.8
        # 新增一列耗时率
        num_df['耗时率'] = 10.16

        # 匹配参数，读取json文件获取项目类型
        dev_param = 0.7
        inte_param = 0.5
        params = [10, 7, 4, 5, 4]
        # 查询是否存在config.json文件再读取json文件
        json_path = os.path.join(
            UPLOAD_DIRECTORY, username, project_name, 'config.json')
        config = {}
        if not os.path.exists(json_path):
            project_type = '应用系统类'
            deploy_method = '网一级部署模式'
        else:
            with open(json_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            project_type = config.get('project_type', '应用系统类')
            deploy_method = config.get('system_deployment_mode', '网一级部署模式')
        if project_type == '技术支持平台':
            params = [15, 10, 6, 7, 6]
        if project_type == '数据分析应用':
            dev_param = 0.55
        if deploy_method == "网一级部署模式":
            inte_param = 0.75
        elif deploy_method in ["网省两级部署模式", "网一级管理节点和省一级分节点部署模式"]:
            inte_param = 1.0

        # 逐行增加一列功能点数、功能点法预算工作量(人天)、开发(集成)工作量(人天)
        for i, r in num_df.iterrows():
            num_df.at[i, '功能点数'] = ((int(r['内部逻辑文件修改数'])*0.4+int(r['内部逻辑文件新增数']))*params[0] +
                                    (int(r['外部接口文件修改数'])*0.5+int(r['外部接口文件新增数']))*params[1] +
                                    (int(r['输入操作项修改数'])*0.4+int(r['输入操作项新增数']))*params[2] +
                                    (int(r['输出操作项修改数'])*0.4+int(r['输出操作项新增数']))*params[3] +
                                    (int(r['查询操作项修改数'])*0.4+int(r['查询操作项新增数']))*params[4])*0.8*1.85*1.23
            num_df.at[i, '功能点法预算工作量(人天)'] = round(
                num_df.at[i, '功能点数'] * 10.16 / 8, 2)
            num_df.at[i, '开发(集成)工作量(人天)'] = round(
                num_df.at[i, '功能点法预算工作量(人天)'] * dev_param, 2)

        # 增加计算开发工作量总和
        sum_dev = num_df['开发(集成)工作量(人天)'].sum()*2602/10000

        if inte_df is not None and not inte_df.empty:
            # 深度复制集成数据
            num_inte_df = inte_df.copy(deep=True)
            num_inte_df['通用度因子'] = 0.8
            num_inte_df['耗时率'] = 6.9
            for i, r in num_inte_df.iterrows():
                num_inte_df.at[i, '功能点数'] = ((int(r['内部逻辑文件修改数'])*0.4+int(r['内部逻辑文件新增数']))*params[0] +
                                             (int(r['外部接口文件修改数'])*0.5+int(r['外部接口文件新增数']))*params[1] +
                                             (int(r['输入操作项修改数'])*0.4+int(r['输入操作项新增数']))*params[2] +
                                             (int(r['输出操作项修改数'])*0.4+int(r['输出操作项新增数']))*params[3] +
                                             (int(r['查询操作项修改数'])*0.4+int(r['查询操作项新增数']))*params[4])*0.8*1.85*1.23
                num_inte_df.at[i, '功能点法预算工作量(人天)'] = round(
                    num_inte_df.at[i, '功能点数'] * 6.9 / 8, 2)
                num_inte_df.at[i, '开发(集成)工作量(人天)'] = round(
                    num_inte_df.at[i, '功能点法预算工作量(人天)'] * 7 / 9 * inte_param, 2)
            sum_inte = num_inte_df['开发(集成)工作量(人天)'].sum()*2602/10000
            sum_dev += sum_inte
            num_df = pd.concat([num_df, num_inte_df], ignore_index=True)

        # 保存到config
        config['dev_fee'] = sum_dev
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)

        # 将app_df填充空白行列后，存为一个临时的json文件用于再读取
        try:
            table3_1_df = insert_start_pos(num_df, start_pos)
            user_project_directory = os.path.join(
                UPLOAD_DIRECTORY, username, project_name, 'table3_1.json')
            table3_1_df.to_json(user_project_directory,
                                orient='records', force_ascii=False)
        except Exception as e:
            logger.error(f"保存临时json文件失败！{e}")
        finally:
            return num_df
    except Exception as e:
        logger.error(f"获取数值版表三附1失败！{e}")
        return None


def get_ilf_dict(username: str, project_name: str):
    """
    获取内部逻辑文件字典。
    Args:
        username (str): 用户名
        project_name (str): 项目名称
    Returns:
        ilf_dict (Dict): 内部逻辑文件字典
        mark_list (List): 实体不一致的备注列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    # 初始化内部逻辑文件字典
    ilf_dict = {}
    mark_list = []
    try:
        # 读取excel文件
        raw_df = read_app_func(username, project_name, '应用功能', (3, 1))
        if raw_df is None:
            return {}, []
        # 取其中的逻辑实体列和状态列
        logic_entity_df = raw_df.loc[:, ['逻辑实体', '状态']]
        # 若不为空
        if not logic_entity_df.empty:
            # 遍历逻辑实体列
            for _, row in logic_entity_df.iterrows():
                # 取逻辑实体列的值
                logic_entity_row = row['逻辑实体']
                status_row = row['状态']
                # 取状态列的值
                if "建设中" in status_row:
                    status = "建设中"
                elif "已建设" in status_row:
                    status = "已建设"
                else:
                    status = "待立项"
                # 如果逻辑实体列的值不为空
                if logic_entity_row != '':
                    # 将逻辑实体列进行拆分
                    logic_entity_list = logic_entity_row.split('、')
                    # 遍历逻辑实体列的值
                    for logic_entity in logic_entity_list:
                        # 如果逻辑实体列的值不在字典中
                        if logic_entity not in ilf_dict:
                            # 将逻辑实体列的值作为键，状态列的值作为值，存入字典
                            ilf_dict[logic_entity] = status
                        elif logic_entity not in mark_list and status != ilf_dict[logic_entity]:
                            # 如果逻辑实体列的值在字典中而且状态和字典中已有状态不一致，记录该实体以生成备注
                            mark_list.append(logic_entity)
        return ilf_dict, mark_list
    except Exception as e:
        logger.error(f"获取内部逻辑文件字典失败！{e}")
        return ilf_dict, mark_list


def get_eif_dict(app_df: pd.DataFrame, username: str, project_name: str):
    """
    获取外部接口文件字典。
    Args:
        app_df (pandas.DataFrame): 应用功能数据
        username (str): 用户名
        project_name (str): 项目名称
    Returns:
        eif_dict (Dict): 外部接口文件字典
        mark_list (List): 实体不一致的备注列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    # 初始化外部接口文件字典
    eif_dict = {}
    mark_list = []
    try:
        # 读取excel文件
        raw_df = read_app_func(username, project_name, '应用交互', (1, 1))
        if raw_df is None:
            return {}, []
        # 取其中的输入应用组、概念实体、类型列
        eif_entity = raw_df.loc[:, ['输入应用组', '概念实体', '类型']]
        # 取应用df中的应用组形成内部应用组列表
        internal_app_group = app_df['应用组'].unique()
        # 遍历
        for _, row in eif_entity.iterrows():
            # 取输入应用组列的值
            input_group = row['输入应用组']
            if input_group not in internal_app_group:
                # 如果输入应用组列的值不在内部应用组列表中
                # 取概念实体列的值并拆分
                concept_entity = row['概念实体'].split('、')
                # 取类型列的值
                if "改造" in row['类型']:
                    entity_type = "改造"
                else:
                    entity_type = "新增"
                # 遍历概念实体列的值
                for eif in concept_entity:
                    if eif not in eif_dict:
                        eif_dict[eif] = entity_type
                    elif eif not in mark_list and entity_type != eif_dict[eif]:
                        mark_list.append(eif)
        return eif_dict, mark_list
    except Exception as e:
        logger.error(f"获取外部接口文件字典失败！{e}")
        return eif_dict, mark_list


async def judge_2_3(func_df: pd.DataFrame, username: str, project_name: str):
    """
    根据厂家资料判断两点，根据大模型判断三点。
    Args:
        func_df (pandas.DataFrame): 应用功能数据
        username (str): 用户名
        project_name (str): 项目名称
    Returns:
        func_df (pandas.DataFrame): 应用功能数据
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 初始化列表，包含内部逻辑、外部接口、输入、输出、查询、备注列
        ilf_name, ilf_changed, ilf_regulate, ilf_new = [], [], [], []
        eif_name, eif_changed, eif_regulate, eif_new = [], [], [], []
        # llm_ilf_name, llm_ilf_changed, llm_ilf_regulate, llm_ilf_new = [], [], [], [] # Replaced by direct assignment
        # llm_eif_name, llm_eif_changed, llm_eif_regulate, llm_eif_new = [], [], [], [] # Replaced by direct assignment
        ei_name, ei_changed, ei_regulate, ei_new = [], [], [], []
        eo_name, eo_changed, eo_regulate, eo_new = [], [], [], []
        eq_name, eq_changed, eq_regulate, eq_new = [], [], [], []
        desc_mark_list, ilf_mark_list_excel, eif_mark_list_excel = [], [], []
        # llm_ilf_mark, llm_eif_mark = [], [] # Will be populated by judge_change_add
        # 是否用大模型覆盖excel结果
        isLLM = True  # This flag will determine if we use LLM results or Excel-derived results

        # 遍历每一行功能项，获取其内部逻辑文件、外部接口 (Excel-based)
        if isLLM == False:
            # 提取内部逻辑实体字典用以判断内部逻辑文件的新增修改 (Excel-based)
            ilf_dict_excel, ilf_conflict_mark_excel = get_ilf_dict(
                username, project_name)
            # 提取外部接口实体字典用以判断外部接口文件的新增修改 (Excel-based)
            eif_dict_excel, eif_conflict_mark_excel = get_eif_dict(
                func_df, username, project_name)
            for i, row in func_df.iterrows():
                # Excel-based ILF processing
                logic_entity_row_excel = row['逻辑实体'].split('、')
                mark_temp_excel_ilf = []
                add_excel_ilf, regulate_excel_ilf, change_excel_ilf = 0, 0.4, 0
                valid_logic_entities_excel = []
                for logic_entity in logic_entity_row_excel:
                    if logic_entity and logic_entity not in ["", "\\", "/", " "]:
                        valid_logic_entities_excel.append(logic_entity)
                        if logic_entity in ilf_dict_excel:
                            if logic_entity in ilf_conflict_mark_excel:
                                mark_temp_excel_ilf.append(logic_entity)
                            if ilf_dict_excel[logic_entity] == '待立项':
                                add_excel_ilf += 1
                            elif ilf_dict_excel[logic_entity] == '建设中':
                                change_excel_ilf += 1
                        else:
                            add_excel_ilf += 1
                ilf_name.append('，'.join(valid_logic_entities_excel)
                                if valid_logic_entities_excel else row['逻辑实体'])
                ilf_changed.append(change_excel_ilf)
                ilf_regulate.append(regulate_excel_ilf)
                ilf_new.append(add_excel_ilf)
                ilf_mark_list_excel.append('、'.join(
                    mark_temp_excel_ilf) + '等实体在厂家资料中出现冲突，请检查' if mark_temp_excel_ilf else '')
                # Excel-based EIF processing
                concept_entity_row_excel = row['概念实体'].split('、')
                name_temp_excel_eif = []
                mark_temp_excel_eif = []
                add_excel_eif, regulate_excel_eif, change_excel_eif = 0, 0.5, 0
                for concept_entity in concept_entity_row_excel:
                    if concept_entity and concept_entity not in ["", "\\", "/", " "]:
                        if concept_entity in eif_dict_excel:
                            if concept_entity in eif_conflict_mark_excel:
                                mark_temp_excel_eif.append(concept_entity)
                            if eif_dict_excel[concept_entity] == '新增':
                                add_excel_eif += 1
                                name_temp_excel_eif.append(concept_entity)
                            elif eif_dict_excel[concept_entity] == '改造':
                                change_excel_eif += 1
                                name_temp_excel_eif.append(concept_entity)
                eif_name.append('，'.join(name_temp_excel_eif))
                eif_changed.append(change_excel_eif)
                eif_regulate.append(regulate_excel_eif)
                eif_new.append(add_excel_eif)
                eif_mark_list_excel.append('、'.join(
                    mark_temp_excel_eif) + '等实体在厂家资料中出现冲突，请检查' if mark_temp_excel_eif else '')

        # Call the (now batching) judge_change_add for LLM-based processing
        # This will populate its own columns in func_df like '功能描述备注', '内部逻辑文件' (LLM version), etc.
        # Process a copy to get LLM results
        func_df_llm_processed = await judge_change_add(func_df.copy())

        # Now, merge or select based on isLLM flag
        if isLLM:
            # Use results from func_df_llm_processed for LLM-dependent columns
            # These columns should already be in func_df_llm_processed by the modified judge_change_add
            # We just need to ensure they are correctly named and transferred if not already part of func_df
            func_df['功能描述备注'] = func_df_llm_processed['功能描述备注']
            func_df['内部逻辑文件'] = func_df_llm_processed['内部逻辑文件']
            func_df['内部逻辑文件修改数'] = func_df_llm_processed['内部逻辑文件修改数']
            func_df['内部逻辑文件调节数'] = func_df_llm_processed['内部逻辑文件调节数']
            func_df['内部逻辑文件新增数'] = func_df_llm_processed['内部逻辑文件新增数']
            # LLM version doesn't produce this remark, or it's part of 功能描述备注
            func_df['内部逻辑文件备注'] = ""

            func_df['外部接口文件'] = func_df_llm_processed['外部接口文件']
            func_df['外部接口文件修改数'] = func_df_llm_processed['外部接口文件修改数']
            func_df['外部接口文件调节数'] = func_df_llm_processed['外部接口文件调节数']
            func_df['外部接口文件新增数'] = func_df_llm_processed['外部接口文件新增数']
            func_df['外部接口文件备注'] = ""

            # EI, EO, EQ are fully from LLM in this path
            func_df['输入操作项'] = func_df_llm_processed['输入操作项']
            func_df['输入操作项修改数'] = func_df_llm_processed['输入操作项修改数']
            func_df['输入操作项调节数'] = func_df_llm_processed['输入操作项调节数']
            func_df['输入操作项新增数'] = func_df_llm_processed['输入操作项新增数']

            func_df['输出操作项'] = func_df_llm_processed['输出操作项']
            func_df['输出操作项修改数'] = func_df_llm_processed['输出操作项修改数']
            func_df['输出操作项调节数'] = func_df_llm_processed['输出操作项调节数']
            func_df['输出操作项新增数'] = func_df_llm_processed['输出操作项新增数']

            func_df['查询操作项'] = func_df_llm_processed['查询操作项']
            func_df['查询操作项修改数'] = func_df_llm_processed['查询操作项修改数']
            func_df['查询操作项调节数'] = func_df_llm_processed['查询操作项调节数']
            func_df['查询操作项新增数'] = func_df_llm_processed['查询操作项新增数']
        else:
            # Use Excel-derived values
            # No LLM processing for description remarks in this path
            func_df['功能描述备注'] = ""
            func_df['内部逻辑文件'] = ilf_name
            func_df['内部逻辑文件修改数'] = ilf_changed
            func_df['内部逻辑文件调节数'] = ilf_regulate
            func_df['内部逻辑文件新增数'] = ilf_new
            func_df['内部逻辑文件备注'] = ilf_mark_list_excel

            func_df['外部接口文件'] = eif_name
            func_df['外部接口文件修改数'] = eif_changed
            func_df['外部接口文件调节数'] = eif_regulate
            func_df['外部接口文件新增数'] = eif_new
            func_df['外部接口文件备注'] = eif_mark_list_excel

            # EI, EO, EQ from LLM part of judge_change_add needs to be integrated here too,
            # or judge_change_add needs to be called regardless of isLLM for these parts.
            # For now, assuming these also come from the LLM process or are empty.
            # This part needs careful review of desired logic if isLLM=False.
            # Let's assume we still run LLM for EI, EO, EQ parts.
            # ... and so on for all EI, EO, EQ fields from func_df_llm_processed
            func_df['输入操作项'] = func_df_llm_processed['输入操作项']
            func_df['输入操作项修改数'] = func_df_llm_processed['输入操作项修改数']
            func_df['输入操作项调节数'] = func_df_llm_processed['输入操作项调节数']
            func_df['输入操作项新增数'] = func_df_llm_processed['输入操作项新增数']
            func_df['输出操作项'] = func_df_llm_processed['输出操作项']
            func_df['输出操作项修改数'] = func_df_llm_processed['输出操作项修改数']
            func_df['输出操作项调节数'] = func_df_llm_processed['输出操作项调节数']
            func_df['输出操作项新增数'] = func_df_llm_processed['输出操作项新增数']
            func_df['查询操作项'] = func_df_llm_processed['查询操作项']
            func_df['查询操作项修改数'] = func_df_llm_processed['查询操作项修改数']
            func_df['查询操作项调节数'] = func_df_llm_processed['查询操作项调节数']
            func_df['查询操作项新增数'] = func_df_llm_processed['查询操作项新增数']

        return func_df

    except Exception as e:
        logger.error(f"judge_2_3 执行过程中发生错误: {e}", exc_info=True)
        logger.info("尝试直接使用LLM五点法判断（回退）！")
        # Fallback to original judge_change_add if judge_2_3 fails catastrophically before LLM call.
        # Ensure the func_df passed to judge_change_add here is the original one.
        # This will use the batch processing
        func_df_fallback = await judge_change_add(func_df)
        return func_df_fallback


async def get_table3_1_excel(raw_df: pd.DataFrame, username: str, project_name: str, start_pos: tuple = (14, 1)):
    """
    获取表三附1excel。
    Args:
        username (str): 用户名
        project_name (str): 项目名称
        start_pos (tuple): 起始位置，默认为(14, 1)
    Returns:
        app_df (pandas.DataFrame): 表三附1dataframe
        mark_list (List): 备注列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        mark_list = []
        col_list = ['序号', '应用组', '一级应用模块', '二级应用模块',
                    '功能项', '功能子项', '功能描述', '逻辑实体', '概念实体']

        # 检查列数是否超过col_list的长度，若不足则用空字符串补齐，列名为对应的col_list
        len_raw = raw_df.shape[1]
        len_col = len(col_list)
        if len_raw < len_col:
            len_add = len_col - len_raw
            # 添加空字符串列
            for i in range(len_add):
                column_name = col_list[len_raw + i]
                raw_df[column_name] = ''

        # 检查列名是否包含所需列，若存在则按照列名提取，若不存在则直接按对应序号提取
        app_df = pd.DataFrame()
        for i in range(len_col):
            col = col_list[i]
            if col in raw_df.columns:
                # 检查raw_df[col]列中是否存在空值，若存在则填充空字符串
                for j in range(len(raw_df[col])):
                    if pd.isna(raw_df.loc[j, col]):
                        raw_df.loc[j, col] = ''
                app_df[col] = raw_df[col]
            else:
                # 若功能项不存在则读取第四列
                if col == '功能项':
                    app_df[col] = raw_df.iloc[:, 3]
                elif col == '二级应用模块':
                    app_df[col] = raw_df.iloc[:, 2]
                else:
                    app_df[col] = raw_df.iloc[:, i]

        # 替换所有单独出现的 '-' 和 '/'
        app_df = app_df.map(replace_standalone)
        # 补充逻辑实体
        app_df = validate_logic_entity(app_df)
        # 去除功能描述为空的行
        app_df = app_df[app_df['功能描述'] != '']
        # 删除功能描述中的输入信息、输出信息、查询内容
        app_df['功能描述'] = app_df['功能描述'].apply(delete_contents)

        # 根据厂家资料判断两点，根据大模型判断三点
        app_df = await judge_2_3(app_df, username, project_name)
        app_df = app_df.map(replace_standalone)
        app_df['序号'] = range(1, len(app_df) + 1)

        # 暂时移除
        # 按照应用组、一级应用模块、二级应用模块、功能项的顺序依次进行排序，并重置序号列和索引
        # app_df = app_df.sort_values(by=['应用组', '一级应用模块', '二级应用模块', '功能项'])
        # app_df.reset_index(drop=True, inplace=True)

        # 根据备注列的内容生成备注列表
        for i, row in app_df.iterrows():
            if row['功能描述备注'] != '':
                mark_list.append(
                    {'row': start_pos[0] + i, 'col': start_pos[1] + 6, 'content': row['功能描述备注']})
            if row['内部逻辑文件备注'] != '':
                mark_list.append(
                    {'row': start_pos[0] + i, 'col': start_pos[1] + 7, 'content': row['内部逻辑文件备注']})
            if row['外部接口文件备注'] != '':
                mark_list.append(
                    {'row': start_pos[0] + i, 'col': start_pos[1] + 11, 'content': row['外部接口文件备注']})
        # 删除多余列
        app_df.drop(columns=['逻辑实体', '概念实体', '功能描述备注',
                    '内部逻辑文件备注', '外部接口文件备注'], inplace=True)

        return app_df, mark_list
    except Exception as e:
        logger.error(f"获取表三附1excel失败！{str(e)}")
        return None, None


async def get_inte_word(username: str, project_name: str, start_pos: tuple = (14, 1)):
    """
    从word获取系统集成部分数据。
    Args:
        username (str): 用户名
        project_name (str): 项目名称
    Returns:
        inte_df (pandas.DataFrame): 系统集成dataframe
        inte_mark_list (List): 备注列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    # 创建DataFrame
    columns = ['序号', '应用组', '一级应用模块', '二级应用模块', '功能项', '功能子项', '功能描述']
    inte_raw_df = pd.DataFrame(columns=columns)
    try:
        # 初始化
        rows = []  # 用于保存所有行的数据
        last_row_data = {}  # 用于保存上一行的部分数据

        # 读取word并将正文style设置为10便于判断
        origin_results = docx_search(username, project_name, '应用交互')
        if not origin_results:
            raise HTTPException(status_code=404, detail="未找到word中应用交互相关资料！")
        content = []
        for i in range(len(origin_results)):
            if origin_results[i]['style'] == '0':
                origin_results[i]['style'] = '10'
            if int(origin_results[i]['style']) > 0:
                content.append(origin_results[i])

        # 序号初始化
        i = 0
        index = 1
        while i < len(content):
            if content[i]['style'] == '4':
                last_row_data['应用组'] = "系统集成"
                last_row_data['一级应用模块'] = project_name
                i += 1
            elif content[i]['style'] == '5':
                last_row_data['二级应用模块'] = content[i]['content']
                last_row_data['功能项'] = content[i]['content']
                last_row_data['功能子项'] = ''
                i += 1
                # 继续往下判断直到遇到更高级的标题或者内容结束，即往下一直读正文内容
                while i < len(content) and int(content[i]['style']) > 5:
                    # 若当前内容为正文，则判断是否已有功能描述，若没有则新增，有则续上
                    if content[i]['style'] == '10' and not last_row_data.get('功能描述'):
                        last_row_data['功能描述'] = content[i]['content']
                    elif content[i]['style'] == '10' and last_row_data.get('功能描述'):
                        last_row_data['功能描述'] += '\n' + content[i]['content']
                    i += 1
                # 判断是否已有功能描述，若有则保存
                if last_row_data.get('功能描述'):
                    rows.append({**{'序号': index}, **last_row_data.copy()})
                    index += 1
                    last_row_data['功能描述'] = ""
            else:
                i += 1

        inte_raw_df = pd.DataFrame(rows, columns=columns)

        return inte_raw_df, []

    except Exception as e:
        logger.error(f"从word中生成系统集成部分失败！{e}")
        return inte_raw_df, []


async def get_inte_excel(inte_raw: pd.DataFrame, username: str, project_name: str, start_pos: tuple = (14, 1), app_df: pd.DataFrame = None):
    """
    从excel获取系统集成部分数据。
    Args:
        inte_raw (pandas.DataFrame): 原始数据
        username (str): 用户名
        project_name (str): 项目名称
        start_pos (tuple): 起始位置，默认为(14, 1)
        app_df (pandas.DataFrame): 应用功能数据
    Returns:
        inte_df (pandas.DataFrame): 系统集成dataframe
        inte_mark_list (List): 备注列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        # 初始化
        _, group_name = parse_project_name(project_name)
        col_list = ['序号', '应用组', '一级应用模块', '二级应用模块', '功能项', '功能子项', '功能描述']
        inte_df = pd.DataFrame(columns=col_list)
        raw_col = ['输入应用组', '输入一级应用模块', '输出应用组', '输出一级应用模块', '应用协作描述']
        inte_raw = inte_raw.loc[:, raw_col]
        inte_raw.dropna(inplace=True)
        mark_list = []
        if app_df is not None:
            start_index = len(app_df) + 1
        else:
            start_index = 1
        # 对原始df中的每一行进行生成
        if inte_raw is not None and not inte_raw.empty:
            for row in range(len(inte_raw)):
                desc = inte_raw.iloc[row]['应用协作描述']
                if desc not in ["", "\\", "/", "-"]:
                    group_in = inte_raw.iloc[row]['输入应用组']
                    first_in = inte_raw.iloc[row]['输入一级应用模块']
                    group_out = inte_raw.iloc[row]['输出应用组']
                    first_out = inte_raw.iloc[row]['输出一级应用模块']
                    # 判断输入输出应用组哪一个包含项目名称
                    if group_name in first_in:
                        group_app = group_in
                        second_app = "与" + group_out + "集成"
                    elif group_name in first_out:
                        group_app = group_out
                        second_app = "与" + group_in + "集成"
                    else:
                        group_app = "应用交互"
                        second_app = "与" + group_out + "集成"
                    # 生成df
                    temp_df = pd.DataFrame(
                        [["", "系统集成", second_app, second_app, second_app, second_app, desc]], columns=col_list)
                    inte_df = pd.concat([inte_df, temp_df], ignore_index=True)
        if not inte_df.empty:
            # 删除功能描述中的输入信息、输出信息、查询内容
            inte_df['功能描述'] = inte_df['功能描述'].apply(delete_contents)
            inte_df = await judge_eif(inte_df)
            # 按照应用组、一级应用模块、二级应用模块、功能项的顺序依次进行排序，并重置序号列和索引
            inte_df = inte_df.sort_values(
                by=['应用组', '一级应用模块', '二级应用模块', '功能项'])
            inte_df['序号'] = range(start_index, len(inte_df) + start_index)
            inte_df.reset_index(drop=True, inplace=True)

            # 根据功能描述备注列的内容生成备注列表
            for i, row in inte_df.iterrows():
                # 取序号
                index = inte_df.iloc[i]['序号'] + start_pos[0] - 1
                if row['功能描述备注'] != "":
                    mark_list.append(
                        {'row': index, 'col': start_pos[1] + 6, 'content': row['功能描述备注']})
            inte_df.drop(columns=['功能描述备注'], inplace=True)

        return inte_df, mark_list

    except Exception as e:
        logger.error(f"从excel中生成系统集成部分失败！{str(e)}")
        return None, []


def cut_app_inte(raw_df: pd.DataFrame):
    """
    将应用功能部分和系统集成部分进行切割。
    Args:
        raw_df (pandas.DataFrame): 原始数据
    Returns:
        app_raw (pandas.DataFrame): 切割后的应用功能部分数据
        inte_raw (pandas.DataFrame): 切割后的系统集成部分数据
    """
    # 初始化
    app_raw = pd.DataFrame()
    inte_raw = pd.DataFrame()
    # 去除原始数据中第二列为总计之后的行

    # 遍历每一行，若第三列中包含集成两个字，则将该行保存到inte_df中，否则保存到app_df中
    for i in range(len(raw_df)):
        if raw_df.iloc[i, 2] == '总计':
            break
        if '集成' in str(raw_df.iloc[i, 2]):
            inte_raw = pd.concat(
                [inte_raw, raw_df.iloc[[i]]], ignore_index=True)
        else:
            app_raw = pd.concat([app_raw, raw_df.iloc[[i]]], ignore_index=True)
    # 重置索引
    app_raw.reset_index(drop=True, inplace=True)
    inte_raw.reset_index(drop=True, inplace=True)

    return app_raw, inte_raw


def inte_change(raw_df: pd.DataFrame, app_df: pd.DataFrame, project_name: str):
    """
    对系统集成部分数据进行处理，去除不必要的列。
    Args:
        raw_df (pandas.DataFrame): 系统集成原始数据
        app_df (pandas.DataFrame): 应用功能数据
    Returns:
        inte_raw (pandas.DataFrame): 处理后的系统集成数据
    """
    # 初始化
    col_list = ['输入应用组', '输入一级应用模块', '输出应用组', '输出一级应用模块', '应用协作描述']
    inte_raw = pd.DataFrame(columns=col_list)
    # 取app_df中最后一行的应用组作为输入应用组的值
    in_group = app_df.iloc[-1]['应用组']
    # 遍历原始数据的每一行，取其中的一级应用模块或者二级功能列作为输出应用组和输出一级应用模块
    if "二级功能" in raw_df.columns:
        inte_index = 2
    else:
        inte_index = 3
    for i in range(len(raw_df)):
        # 取inte_index列的值作为输出应用组和输出一级应用模块，并删除第一个与字和最后集成两个字
        out_app = raw_df.iloc[i, inte_index]
        if out_app.startswith("与"):
            out_app = out_app[1:]
        if out_app.endswith("集成"):
            out_app = out_app[:-2]
        # 取功能描述列作为应用协作描述
        desc = raw_df.loc[i, "功能描述"]
        # 生成一行数据
        temp_df = pd.DataFrame([{
            '输入应用组': in_group,
            '输入一级应用模块': project_name,
            '输出应用组': out_app,
            '输出一级应用模块': out_app,
            '应用协作描述': desc
        }])
        inte_raw = pd.concat([inte_raw, temp_df], ignore_index=True)
    # 重置索引
    inte_raw.reset_index(drop=True, inplace=True)

    return inte_raw


def validate_ilf(func_df: pd.DataFrame, mark_list: list):
    """
    根据内部逻辑文件清单，校验内部逻辑文件列中是否出现重复值

    Args:
        func_df (pandas.DataFrame): 应用功能数据
        mark_list (list): 备注列表
    Returns:
        func_df (pandas.DataFrame): 校验后的应用功能数据
        mark_list (list): 备注列表
    """
    try:
        # 读取内部逻辑文件清单
        ilf_file = os.path.join(PROJECT_ROOT, 'documents', '内部逻辑文件.csv')
        ea_ilf_file = os.path.join(PROJECT_ROOT, 'documents', 'tugraph', 'entity', '030-逻辑实体.csv')
        ilf_df = pd.read_csv(ilf_file, dtype=str)
        ea_ilf_df = pd.read_csv(ea_ilf_file, dtype=str)
        ilf_list = set(ilf_df['内部逻辑文件清单'].tolist())
        ea_ilf_list = set(ea_ilf_df['name'].tolist())
        for i in range(len(func_df)):
            ilf_names = func_df.loc[i, '内部逻辑文件'].split('；')
            content = ""
            for name in ilf_names:
                if "【" not in name:
                    flag = False
                    if name in ilf_list:
                        index = ilf_df[ilf_df['内部逻辑文件清单']==name].index[0]
                        # 在ilf_df中匹配对应的建设年份以及所属单位
                        unit = ilf_df.loc[index, '所属单位']
                        year = ilf_df.loc[index, '建设年份']
                        project = ilf_df.loc[index, '项目名称']
                        content += f"{name}于{year}{unit}{project}项目中使用，"
                        flag = True
                    if name in ea_ilf_list:
                        content += f"{name}于企业架构中使用，"
                        flag = True
                    if flag:
                        # 对应的新增数-1，修改数+1
                        func_df.loc[i, '内部逻辑文件新增数'] -= 1
                        func_df.loc[i, '内部逻辑文件修改数'] += 1
            if content != "":
                content = content[:-1] + "。"
                mark_list.append({'row': i + 14, 'col': 7 + 1, 'content': content})
        return func_df, mark_list
    except Exception as e:
        logger.error(f"校验内部逻辑文件失败！{e}")
        return func_df, mark_list


async def get_table3_1_content(username, project_name, start_pos=(14, 1)):
    """
    获取表三附1二维表格内容。
    Args:
        username (str): 用户名
        project_name (str): 项目名称
        start_pos (tuple): 起始位置，默认为(14, 1)
    Returns:
        content_list (List): excel内容的二维列表
        mark_list (List): 备注列表
        merged_list (List): 合并单元格的列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    try:
        inte_raw = pd.DataFrame()
        raw_df = read_app_func(username, project_name,
                               sheet_name="应用功能", start_pos=(3, 1))
        if raw_df is None:
            # 若未找到应用功能sheet则尝试找“表三附1（开发、集成工作量测算）”
            raw_df = read_app_func(username, project_name,
                                   sheet_name="表三附1（开发、集成工作量测算） ", start_pos=(13, 1))
        if raw_df is None:
            # 若未找到应用功能sheet则尝试找“表三附1（开发、集成工作量测算） ”
            raw_df = read_app_func(username, project_name,
                                   sheet_name="表三附1（开发、集成工作量测算）", start_pos=(13, 1))

        if raw_df is None:
            # 若未找到对应excel版厂家资料则尝试找word版厂家资料
            app_df = read_word_func(username, project_name)
            if app_df is None:
                logger.error(f"未找到对应word厂家资料: username={username}, project_name={project_name}")
                raise HTTPException(status_code=404, detail="未找到对应word厂家资料！")
            app_df['逻辑实体'] = ""
            mark_list = []
            # 补充逻辑实体
            app_df = validate_logic_entity(app_df)
            # 删除功能描述中的输入信息、输出信息、查询内容
            app_df['功能描述'] = app_df['功能描述'].apply(delete_contents)

            # 使用大模型判断更改新增数并替换其中的-/0
            app_df = await judge_change_add(app_df)
            app_df = app_df.map(replace_standalone)
            app_df['序号'] = range(1, len(app_df) + 1)

            # 移除分组功能
            # 按照应用组、一级应用模块、二级应用模块、功能项的顺序依次进行排序，并重置序号列和索引
            # app_df = app_df.sort_values(by=['应用组', '一级应用模块', '二级应用模块', '功能项'])
            # app_df.reset_index(drop=True, inplace=True)

            # 根据功能描述备注列的内容生成备注列表
            for i, row in app_df.iterrows():
                if row['功能描述备注'] != "":
                    mark_list.append(
                        {'row': start_pos[0] + i, 'col': start_pos[1] + 6, 'content': row['功能描述备注']})
            app_df.drop(columns=['逻辑实体', '功能描述备注'], inplace=True)
        else:
            raw_df, inte_raw = cut_app_inte(raw_df)
            app_df, mark_list = await get_table3_1_excel(
                raw_df, username, project_name, start_pos)

        # 检查是否列名是否为模板列名，若不是则新增列并插入空值
        func_df = validate_shape(app_df)

        # 系统集成
        if inte_raw is None or inte_raw.empty:
            inte_raw = read_app_func(username, project_name,
                                     sheet_name="应用交互", start_pos=(1, 1))
        else:
            inte_raw = inte_change(inte_raw, app_df, project_name)
        if inte_raw is None:
            inte_df, inte_mark = await get_inte_word(username, project_name)
        else:
            inte_df, inte_mark = await get_inte_excel(inte_raw, username, project_name, start_pos, app_df)

        # 去除重复的文件名，耗时太久，暂时去除
        # app_df, int_df = dedup_lf_name(func_df, inte_df, '内部逻辑文件')
        # app_df, int_df = dedup_lf_name(func_df, inte_df, '外部接口文件')

        # 拼接inte_df和app_df
        if inte_df is not None and not inte_df.empty:
            sum_df = pd.concat([func_df, inte_df], ignore_index=True)
            mark_list.extend(inte_mark)
        else:
            sum_df = func_df
        sum_df = handle_duplicate_values(sum_df, '内部逻辑文件')
        sum_df = handle_duplicate_values(sum_df, '外部接口文件')

        # 校验内部逻辑文件
        sum_df, mark_list = validate_ilf(sum_df, mark_list)

        # 计算融合表格坐标，应用组、一级应用模块、二级应用模块、功能项相同的项进行合并
        merged_list = []
        for col in ['应用组', '一级应用模块', '二级应用模块', '功能项']:
            col_index = sum_df.columns.get_loc(col) + start_pos[1]
            group_index = get_start_end(sum_df, col)
            if group_index:
                for group in group_index:
                    start = group['start']
                    end = group['end']
                    if start != end:
                        merged_list.append((start_pos[0] + start, col_index,
                                           start_pos[0] + end, col_index))
        # 对最后一行的前6个列进行合并
        merged_list.append((start_pos[0] + len(sum_df), start_pos[1],
                           start_pos[0] + len(sum_df), start_pos[1] + 6))

        # 获取公式版表三附一和计算临时数值版表三附一
        # 先按照原来app_df和inte_df的长度进行拆分
        app_df = sum_df.iloc[:len(func_df), :]
        int_df = sum_df.iloc[len(func_df):, :]
        formula_df = formula_table3_1(sum_df, int_df, start_pos)
        num_table3_1(app_df, int_df, start_pos, username, project_name)

        return formula_df, mark_list, merged_list
    except Exception as e:
        logger.error(f"生成表三附1内容失败！{e}")
        return None, None, None


@router.post("/time_eval", summary="预估表三附一时间")
@api_response
async def time_eval(request: ExcelRequest):
    """
    Args:
        request (ProjectInfoRequest): 包含用户输入的项目名称
            - user_name (str): 用户名
            - project_name (str): 格式为"项目类（具体项目名称）"
            - start_row (int): 起始行号
            - start_col (int): 起始列号
    Returns:
        APIResponse: 包含处理结果的响应对象
            - status_code: 200 校验成功
            - message: 处理结果说明
            - data: 预估时间文本
    Raises:
        HTTPException(500): 服务器内部错误
    """
    result_txt = ""
    try:
        username = request.username
        project_name = request.project_name
        app_df = read_app_func(username, project_name,
                               sheet_name="应用功能", start_pos=(3, 1))
        if app_df is None:
            app_df = read_app_func(username, project_name,
                                   sheet_name="表三附1（开发、集成工作量测算） ", start_pos=(13, 1))
        if app_df is None:
            app_df = read_app_func(username, project_name,
                                   sheet_name="表三附1（开发、集成工作量测算）", start_pos=(13, 1))
        if app_df is None:
            app_df = read_word_func(username, project_name)
            if app_df is None:
                logger.info(f"未识别到word中应用功能内容！")
        
        if app_df is None or app_df.empty:
            logger.info("未识别到厂家资料中中应用功能内容！")
            raise HTTPException(status_code=404, detail="未读取到相关资料！")
        else:
            len_app = len(app_df)
            if len_app < 50:
                result_txt = "预计需要5分钟，请耐心等待！"
            elif len_app < 100:
                result_txt = "预计需要15分钟，请耐心等待！"
            elif len_app < 200:
                result_txt = "预计需要30分钟，请耐心等待！"
            elif len_app < 500:
                result_txt = "预计需要1小时，请耐心等待！"
            elif len_app < 1000:
                result_txt = "预计需要2小时，请耐心等待！"
            elif len_app < 2000:
                result_txt = "预计需要4小时，请耐心等待！"
            else:
                result_txt = "预计需要较长时间，请耐心等待！"

    except Exception as e:
        logger.error(f"预估表三附一生成时间失败：{e}")
        result_txt = "预计需要较长时间，请耐心等待！"
    finally:
        return APIResponse(status_code=200, message="预估完毕！", data=result_txt)


@router.post("/table3_1", summary="生成表三附1")
@api_response
async def table3_1(request: ExcelRequest):
    """
    生成表三附1。
    Args:
        request (ProjectInfoRequest): 包含用户输入的项目名称
            - user_name (str): 用户名
            - project_name (str): 格式为"项目类（具体项目名称）"
            - start_row (int): 起始行号
            - start_col (int): 起始列号
    Returns:
        APIResponse: 包含处理结果的响应对象
            - status_code: 200 校验成功
            - message: 处理结果说明
            - data: 
                - content_list (List): excel内容的二维列表
                - mark_list (List): 包含坐标信息的备注列表
                - merged_list (List): 包含坐标信息的合并单元格列表
    Raises:
        HTTPException(500): 服务器内部错误
    """
    # 获取用户名和项目名称然后查询对应厂家资料，若不存在则返回错误信息提示其上传厂家资料
    try:
        start_pos = (request.start_row, request.start_col)
        app_df, mark_list, merged_list = await get_table3_1_content(
            request.username, request.project_name, start_pos)

        if app_df is None:
            raise HTTPException(status_code=404, detail="生成表三附1失败！未读取到相关资料！")

        # 填充空字符串
        app_df = app_df.fillna("").infer_objects(copy=False)

        # 转化为二维列表输出结果
        content_list = app_df.values.tolist()

        # 添加调试日志检查数据类型
        def check_numpy_types(obj, path=""):
            if isinstance(obj, np.number):
                return True
            elif isinstance(obj, (list, tuple)):
                found = False
                for i, item in enumerate(obj):
                    if check_numpy_types(item, f"{path}[{i}]"):
                        found = True
                return found
            elif isinstance(obj, dict):
                found = False
                for k, v in obj.items():
                    if check_numpy_types(v, f"{path}.{k}"):
                        found = True
                return found
            return False

        # 检查各个列表中的数据类型
        check_numpy_types(content_list, "content_list")
        check_numpy_types(mark_list, "mark_list")
        check_numpy_types(merged_list, "merged_list")

        # 转换NumPy类型为Python原生类型
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, list):
                return [convert_numpy_types(i) for i in obj]
            elif isinstance(obj, tuple):
                return tuple(convert_numpy_types(i) for i in obj)
            elif isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            return obj

        content_list = convert_numpy_types(content_list)
        mark_list = convert_numpy_types(mark_list)
        merged_list = convert_numpy_types(merged_list)

        # 将响应数据转化为json文件写入
        response_data = {
            "status_code": 200,
            "message": "生成表三附1成功！",
            "data": {
                "content_list": content_list,
                "mark_list": mark_list,
                "merged_list": merged_list
            }
        }
        # 将响应数据保存为JSON文件，便于后续访问和处理
        try:
            # 从配置中获取根目录
            ROOT_DIRECTORY = settings.PROJECT_ROOT

            # 构建保存路径
            media_dir = os.path.join(
                ROOT_DIRECTORY, "static", request.username, request.project_name, "media")
            json_path = os.path.join(media_dir, "table3_1.json")

            # 确保目录存在
            os.makedirs(media_dir, exist_ok=True)

            # 写入JSON文件
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(response_data, f, ensure_ascii=False, indent=4)

            logger.info(f"成功保存表三附1数据到: {json_path}")
        except Exception as e:
            # 记录错误但不中断流程，因为即使文件保存失败，API仍可返回数据
            logger.error(f"保存表三附1 JSON文件失败: {str(e)}")

        # 返回API响应
        return APIResponse(
            status_code=200,
            message="生成成功",
            data={
                "content_list": content_list,
                "mark_list": mark_list,
                "merged_list": merged_list
            }
        )

    except Exception as e:
        logger.error(f"生成表三附1失败！{e}")
        # 将响应数据转化为json文件写入
        response_data = {
            "status_code": 500,
            "message": "生成表三附1失败！",
            "data": f"{e}"
        }
        # 将响应数据保存为JSON文件，便于后续访问和处理
        try:
            # 从配置中获取根目录
            ROOT_DIRECTORY = settings.PROJECT_ROOT

            # 构建保存路径
            media_dir = os.path.join(
                ROOT_DIRECTORY, "static", request.username, request.project_name, "media")
            json_path = os.path.join(media_dir, "table3_1.json")

            # 确保目录存在
            os.makedirs(media_dir, exist_ok=True)

            # 写入JSON文件
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(response_data, f, ensure_ascii=False, indent=4)

            logger.info(f"成功保存表三附1数据到: {json_path}")
        except Exception as e:
            # 记录错误但不中断流程，因为即使文件保存失败，API仍可返回数据
            logger.error(f"保存表三附1 JSON文件失败: {str(e)}")

        raise HTTPException(status_code=500, detail=f"服务器内部错误！{e}")
