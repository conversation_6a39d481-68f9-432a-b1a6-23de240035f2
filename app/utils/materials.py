# 本文件用于查询相关文件的内容
# - search_task 根据项目类名称匹配举措及规划任务
import os
import difflib
import pandas as pd
from app.core.logging_config import get_logger
from app.utils.tugraph import search_project
from app.core.config import Settings

logger = get_logger(__name__)

def search_task(project_type):
    '''
    查询包含项目类名称及相关项目库分类名称的规划任务和举措。

    Args:
        project_name: 项目类名称

    Returns:
        task_dict (dict): 项目相关的规划任务和举措，数据结构为{规划任务名称，建设目标，总体要求，规划举措}
    '''
    try:
        # 读取任务举措文件
        settings = Settings()
        measure_path = os.path.join(
            settings.PROJECT_ROOT, 'documents/tugraph/relation/001-任务-举措.csv')
        measure_df = pd.read_csv(measure_path)
        measure_list = list(measure_df['name'])

        # 获取项目库三级分类名称，并匹配最相近的举措
        third_class = search_project(project_type)
        closest_match = difflib.get_close_matches(third_class, measure_list, n=1, cutoff=0.0)
        measure = closest_match[0]

        # 根据举措名称匹配对应的规划任务，返回对应的任务名称、目标、要求
        matching_rows = measure_df[measure_df['name'] == measure]
        start_ids = matching_rows['start_id'].unique()
        task_path = os.path.join(
            settings.PROJECT_ROOT, 'documents/tugraph/entity/001-规划任务.csv')
        task_df = pd.read_csv(task_path)
        tasks = task_df[task_df['id'].isin(start_ids)]
        task_list = tasks.to_dict(orient='records')
        task = task_list[0]['name']
        target = task_list[0]['target']
        require = task_list[0]['requirement']
        task_dict = {
            '规划任务名称': task,
            '建设目标': target,
            '总体要求': require,
            '规划举措': measure
        }
        return task_dict
    except Exception as e:
        logger.info(f"查询项目类相关规划任务出错：{e}")
        return None

if __name__=="__main__":
    logger.debug(search_task('资源租赁'))